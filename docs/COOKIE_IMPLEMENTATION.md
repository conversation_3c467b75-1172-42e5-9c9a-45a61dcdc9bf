# Cookie-Banner Implementation - nDSG Schweiz Konform

Diese Implementierung bietet eine vollständige, nDSG-konforme Cookie-Consent-Management-Lösung für die DB-Performance Website.

## 🎯 Features

### ✅ nDSG-Konformität
- Granulare Cookie-Kategorisierung (Notwendig, Funktional, Analyse, Marketing)
- Explizite Einwilligung für nicht-notwendige Cookies
- Detaillierte Cookie-Informationen mit Zweck, Dauer und Anbieter
- Widerrufsmöglichkeit jederzeit verfügbar
- Transparente Datenschutzinformationen

### ✅ Benutzerfreundlichkeit
- Moderner, responsiver Cookie-Banner
- Einfache und detaillierte Einstellungsoptionen
- Schnellaktionen (Alle akzeptieren/Nur notwendige)
- Dedizierte Cookie-Einstellungsseite
- Persistente Speicherung der Präferenzen

### ✅ Technische Features
- Automatische Script-Blockierung bis zur Einwilligung
- Google Analytics Consent Mode Integration
- Facebook Pixel Consent Integration
- React Hooks für einfache Integration
- TypeScript-Unterstützung
- Event-basierte Consent-Updates

## 📁 Dateistruktur

```
src/
├── components/
│   ├── CookieBanner.tsx           # Hauptkomponente des Cookie-Banners
│   ├── CookieSettingsButton.tsx   # Button für Cookie-Einstellungen
│   └── GoogleAnalytics.tsx        # GA4 mit Consent-Integration
├── hooks/
│   └── useCookieConsent.ts        # React Hooks für Cookie-Consent
├── lib/
│   ├── cookieManager.ts           # Kern-Cookie-Management-Logik
│   └── cookieScriptBlocker.ts     # Automatische Script-Blockierung
├── app/
│   └── cookie-einstellungen/
│       └── page.tsx               # Dedizierte Cookie-Einstellungsseite
└── scripts/
    └── setup-cookie-page.ts       # Sanity CMS Setup-Script
```

## 🚀 Installation & Setup

### 1. Dependencies installieren
```bash
# Alle notwendigen Dependencies sind bereits in package.json enthalten
npm install
```

### 2. Cookie-Einstellungsseite in Sanity erstellen (optional)
```bash
npm run setup:cookies
# oder
npx tsx scripts/setup-cookie-page.ts
```

### 3. Google Analytics ID konfigurieren
Bearbeiten Sie `src/app/layout.tsx` und ersetzen Sie `G-XXXXXXXXXX` mit Ihrer echten GA4 Measurement ID:

```tsx
<GoogleAnalytics measurementId="G-YOUR-ACTUAL-ID" />
```

## 🔧 Verwendung

### Cookie-Banner
Der Cookie-Banner wird automatisch angezeigt, wenn noch keine Einwilligung vorliegt:

```tsx
import { CookieBanner } from '@/components/CookieBanner'

// Bereits in ConditionalLayout.tsx integriert
<CookieBanner />
```

### Cookie-Consent in Komponenten prüfen
```tsx
import { useCookieConsent } from '@/hooks/useCookieConsent'

function MyComponent() {
  const { isAllowed } = useCookieConsent()
  
  if (isAllowed('analytics')) {
    // Analytics-Code ausführen
  }
  
  return <div>...</div>
}
```

### Spezifische Consent-Hooks
```tsx
import { useAnalyticsConsent, useMarketingConsent } from '@/hooks/useCookieConsent'

function AnalyticsComponent() {
  const { isAllowed, isLoading } = useAnalyticsConsent()
  
  if (isLoading) return <div>Loading...</div>
  if (!isAllowed) return null
  
  return <div>Analytics Content</div>
}
```

### Cookie-Einstellungen Button
```tsx
import { CookieSettingsButton } from '@/components/CookieSettingsButton'

// Als Link
<CookieSettingsButton variant="link" />

// Als Button
<CookieSettingsButton variant="button" />

// Als Icon
<CookieSettingsButton variant="icon" />
```

## 📋 Cookie-Kategorien

### 1. Notwendige Cookies (immer aktiv)
- `cookie_consent`: Speichert Cookie-Einstellungen (1 Jahr)
- `session_id`: Session-Verwaltung (Session)

### 2. Funktionale Cookies
- `user_preferences`: Benutzereinstellungen (6 Monate)
- `language_preference`: Spracheinstellung (1 Jahr)

### 3. Analyse-Cookies
- `_ga`, `_ga_*`, `_gid`: Google Analytics (2 Jahre/24h)

### 4. Marketing-Cookies
- `_fbp`, `_fbc`: Facebook Pixel (3 Monate)

## 🔒 Rechtliche Konformität

### nDSG-Anforderungen erfüllt:
- ✅ Transparente Information über Cookie-Verwendung
- ✅ Granulare Einwilligungsmöglichkeiten
- ✅ Einfache Widerrufsmöglichkeit
- ✅ Detaillierte Zweck- und Anbierinformationen
- ✅ Speicherung der Einwilligungsentscheidung
- ✅ Keine Benachteiligung bei Ablehnung

### Zusätzliche Features:
- ✅ Consent Mode für Google Analytics
- ✅ Automatische Script-Blockierung
- ✅ Versionierung der Consent-Richtlinien
- ✅ Event-basierte Consent-Updates

## 🛠 Konfiguration

### Cookie-Definitionen anpassen
Bearbeiten Sie `src/lib/cookieManager.ts` um neue Cookies hinzuzufügen:

```typescript
export const COOKIE_DEFINITIONS: CookieInfo[] = [
  {
    name: 'new_cookie',
    category: 'analytics',
    purpose: 'Beschreibung des Zwecks',
    duration: '1 Jahr',
    provider: 'Anbieter',
    essential: false,
  },
  // ...
]
```

### Script-Blockierung erweitern
Bearbeiten Sie `src/lib/cookieScriptBlocker.ts` um neue Scripts zu kategorisieren:

```typescript
private categorizeScript(script: HTMLScriptElement): CookieCategory | null {
  const src = script.src?.toLowerCase() || ''
  
  if (src.includes('your-analytics-service.com')) {
    return 'analytics'
  }
  
  // ...
}
```

## 🎨 Styling

Das Cookie-Banner verwendet Tailwind CSS-Klassen und folgt dem Swiss Auto Care Design:
- Hauptfarbe: `bg-dark-gray` (#22223B)
- Akzentfarbe: `text-accent-red` (#E63946)
- Responsive Design für alle Bildschirmgrößen

### Anpassung des Designs
Bearbeiten Sie `src/components/CookieBanner.tsx` um das Styling anzupassen.

## 🧪 Testing

### Cookie-Einstellungen testen
1. Öffnen Sie die Entwicklertools (F12)
2. Gehen Sie zu Application > Local Storage
3. Löschen Sie `db_performance_cookie_consent`
4. Laden Sie die Seite neu
5. Der Cookie-Banner sollte erscheinen

### Script-Blockierung testen
1. Öffnen Sie die Entwicklertools > Network
2. Lehnen Sie alle Cookies ab
3. Überprüfen Sie, dass keine Analytics/Marketing-Scripts geladen werden
4. Akzeptieren Sie Analytics-Cookies
5. Überprüfen Sie, dass Google Analytics geladen wird

## 🔄 Updates & Wartung

### Consent-Version aktualisieren
Bei Änderungen der Cookie-Richtlinien:

1. Erhöhen Sie `CONSENT_VERSION` in `src/lib/cookieManager.ts`
2. Benutzer werden automatisch um neue Einwilligung gebeten

### Neue Cookie-Kategorien hinzufügen
1. Erweitern Sie den `CookieCategory` Type
2. Aktualisieren Sie `COOKIE_DEFINITIONS`
3. Passen Sie die UI-Komponenten an

## 📞 Support

Bei Fragen zur Implementation:
- Überprüfen Sie die Konsole auf Fehlermeldungen
- Testen Sie in verschiedenen Browsern
- Überprüfen Sie die Local Storage Einträge

## 🔗 Nützliche Links

- [nDSG Schweiz Informationen](https://www.edoeb.admin.ch/edoeb/de/home/<USER>/Internet_und_Computer/cookies.html)
- [Google Analytics Consent Mode](https://developers.google.com/analytics/devguides/collection/gtagjs/consent-mode)
- [Facebook Pixel Consent](https://developers.facebook.com/docs/facebook-pixel/implementation/gdpr)

---

**Hinweis**: Diese Implementation bietet eine solide Grundlage für nDSG-Konformität. Für spezifische rechtliche Beratung konsultieren Sie bitte einen Datenschutzexperten.
