'use client'

import { strings } from '@/strings'
import { WhatsAppButton } from './WhatsAppButton'
import { CookieSettingsButton } from './CookieSettingsButton'

interface FooterData {
  companyName?: string
  address?: string
  rightsText?: string
  legalSectionTitle?: string
  contactSectionTitle?: string
  phone?: string
  email?: string
  whatsapp?: string
  legalLinks?: {
    imprintText?: string
    privacyText?: string
    termsText?: string
  }
}

interface FooterProps {
  footerData?: FooterData | null
}

export const Footer: React.FC<FooterProps> = ({ footerData }) => {
  // Get current year dynamically
  const currentYear = new Date().getFullYear()

  // Fallback to strings if no data provided
  const getFooterContent = () => {
    if (!footerData) {
      return {
        companyName: strings.footer.company,
        address: strings.footer.address,
        rightsText: strings.footer.rights,
        legalSectionTitle: 'Rechtliches',
        contactSectionTitle: 'Kontakt',
        phone: strings.contact.company.phone,
        email: strings.contact.company.email,
        whatsapp: strings.contact.company.whatsapp,
        legalLinks: {
          imprintText: strings.footer.links.imprint,
          privacyText: strings.footer.links.privacy,
          termsText: strings.footer.links.terms,
        }
      }
    }
    return footerData
  }

  const content = getFooterContent()

  return (
    <footer className="bg-anthracite text-gray-300">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-white text-base font-semibold">Unternehmen</h3>
              <ul className="mt-4 space-y-2">
                <li><a className="hover:text-accent-red transition-colors" href="#">Über uns</a></li>
                <li><a className="hover:text-accent-red transition-colors" href="#">Karriere</a></li>
                <li><a className="hover:text-accent-red transition-colors" href="#">Presse</a></li>
              </ul>
            </div>
            <div>
              <h3 className="text-white text-base font-semibold">Services</h3>
              <ul className="mt-4 space-y-2">
                <li><a className="hover:text-accent-red transition-colors" href="/tuning">Software-Tuning</a></li>
                <li><a className="hover:text-accent-red transition-colors" href="/fahrzeugaufbereitung">Fahrzeugaufbereitung</a></li>
                <li><a className="hover:text-accent-red transition-colors" href="/services">Alle Services</a></li>
              </ul>
            </div>
            <div>
              <h3 className="text-white text-base font-semibold">Rechtliches</h3>
              <ul className="mt-4 space-y-2">
                <li><a className="hover:text-accent-red transition-colors" href="/impressum">{content.legalLinks?.imprintText}</a></li>
                <li><a className="hover:text-accent-red transition-colors" href="/datenschutz">{content.legalLinks?.privacyText}</a></li>
                <li><a className="hover:text-accent-red transition-colors" href="/agb">{content.legalLinks?.termsText}</a></li>
                <li>
                  <CookieSettingsButton
                    variant="link"
                    className="hover:text-accent-red transition-colors text-gray-300"
                    showIcon={false}
                  />
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-white text-base font-semibold">{content.contactSectionTitle}</h3>
              <div className="text-gray-400 space-y-2 mt-4">
                <p>{content.phone}</p>
                <p>{content.email}</p>

                {/* WhatsApp Button */}
                <div className="pt-2">
                  <WhatsAppButton
                    className="text-green-400 hover:text-green-300 text-sm"
                    message={strings.contact.whatsapp.defaultMessage}
                    phoneNumber={content.whatsapp}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="mt-8 border-t border-gray-600 pt-8 text-center">
            <p className="text-gray-400">© {currentYear} {content.companyName}. {content.rightsText}</p>
          </div>
        </div>
      </footer>
  )
} 