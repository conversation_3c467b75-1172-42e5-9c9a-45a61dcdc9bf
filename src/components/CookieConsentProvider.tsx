'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { cookie<PERSON>anager, CookieCategory, CookieConsent } from '@/lib/cookieManager'

interface CookieConsentContextType {
  consent: CookieConsent
  isLoading: boolean
  setConsent: (preferences: Record<CookieCategory, boolean>) => void
  resetConsent: () => void
  isAllowed: (category: CookieCategory) => boolean
  isCookieAllowed: (cookieName: string) => boolean
}

const CookieConsentContext = createContext<CookieConsentContextType | undefined>(undefined)

interface CookieConsentProviderProps {
  children: ReactNode
}

export const CookieConsentProvider = ({ children }: CookieConsentProviderProps) => {
  const [consent, setConsentState] = useState<CookieConsent>(() => ({
    hasConsented: false,
    preferences: {
      necessary: true,
      functional: false,
      analytics: false,
      marketing: false,
    },
    version: '1.0',
  }))
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Load initial consent state
    const currentConsent = cookieManager.getConsent()
    setConsentState(currentConsent)
    setIsLoading(false)

    // Listen for consent changes
    const handleConsentChange = (event: CustomEvent) => {
      setConsentState(event.detail)
    }

    const handleConsentReset = () => {
      setConsentState({
        hasConsented: false,
        preferences: {
          necessary: true,
          functional: false,
          analytics: false,
          marketing: false,
        },
        version: '1.0',
      })
    }

    window.addEventListener('cookieConsentChanged', handleConsentChange as EventListener)
    window.addEventListener('cookieConsentReset', handleConsentReset)

    return () => {
      window.removeEventListener('cookieConsentChanged', handleConsentChange as EventListener)
      window.removeEventListener('cookieConsentReset', handleConsentReset)
    }
  }, [])

  const setConsent = (preferences: Record<CookieCategory, boolean>) => {
    cookieManager.setConsent(preferences)
  }

  const resetConsent = () => {
    cookieManager.resetAllConsent()
  }

  const isAllowed = (category: CookieCategory): boolean => {
    return cookieManager.isCategoryAllowed(category)
  }

  const isCookieAllowed = (cookieName: string): boolean => {
    return cookieManager.isCookieAllowed(cookieName)
  }

  const value: CookieConsentContextType = {
    consent,
    isLoading,
    setConsent,
    resetConsent,
    isAllowed,
    isCookieAllowed,
  }

  return (
    <CookieConsentContext.Provider value={value}>
      {children}
    </CookieConsentContext.Provider>
  )
}

export const useCookieConsentContext = () => {
  const context = useContext(CookieConsentContext)
  if (context === undefined) {
    throw new Error('useCookieConsentContext must be used within a CookieConsentProvider')
  }
  return context
}

/**
 * Hook for checking if a specific cookie category is allowed
 * This version uses the context for better performance
 */
export const useCookieCategoryContext = (category: CookieCategory) => {
  const { isAllowed, isLoading } = useCookieConsentContext()
  
  return {
    isAllowed: isAllowed(category),
    isLoading,
  }
}

/**
 * Hook for Google Analytics consent using context
 */
export const useAnalyticsConsentContext = () => {
  const { isAllowed, isLoading } = useCookieCategoryContext('analytics')
  
  useEffect(() => {
    if (!isLoading && typeof window !== 'undefined' && (window as any).gtag) {
      // Update Google Analytics consent
      (window as any).gtag('consent', 'update', {
        analytics_storage: isAllowed ? 'granted' : 'denied'
      })
    }
  }, [isAllowed, isLoading])
  
  return { isAllowed, isLoading }
}

/**
 * Hook for Marketing/Advertising consent using context
 */
export const useMarketingConsentContext = () => {
  const { isAllowed, isLoading } = useCookieCategoryContext('marketing')
  
  useEffect(() => {
    if (!isLoading && typeof window !== 'undefined') {
      // Update Google Ads consent if gtag is available
      if ((window as any).gtag) {
        (window as any).gtag('consent', 'update', {
          ad_storage: isAllowed ? 'granted' : 'denied',
          ad_user_data: isAllowed ? 'granted' : 'denied',
          ad_personalization: isAllowed ? 'granted' : 'denied'
        })
      }
      
      // Handle Facebook Pixel
      if ((window as any).fbq) {
        if (isAllowed) {
          (window as any).fbq('consent', 'grant')
        } else {
          (window as any).fbq('consent', 'revoke')
        }
      }
    }
  }, [isAllowed, isLoading])
  
  return { isAllowed, isLoading }
}
