'use client'

import { useState } from 'react'
import Link from 'next/link'
import { cookieManager } from '@/lib/cookieManager'

interface CookieSettingsButtonProps {
  variant?: 'link' | 'button' | 'icon'
  className?: string
  showIcon?: boolean
}

export const CookieSettingsButton = ({ 
  variant = 'link', 
  className = '',
  showIcon = true 
}: CookieSettingsButtonProps) => {
  const [showTooltip, setShowTooltip] = useState(false)

  const handleOpenSettings = () => {
    // Option 1: Navigate to settings page
    window.location.href = '/cookie-einstellungen'
    
    // Option 2: Could also trigger a modal here if preferred
    // setShowCookieModal(true)
  }

  const baseClasses = "inline-flex items-center gap-2 transition-colors"
  
  const variantClasses = {
    link: "text-sm text-gray-600 hover:text-accent-red underline",
    button: "px-4 py-2 text-sm border border-gray-300 text-gray-700 hover:bg-gray-100 rounded",
    icon: "p-2 text-gray-600 hover:text-accent-red hover:bg-gray-100 rounded-full"
  }

  const CookieIcon = () => (
    <svg 
      className="w-4 h-4" 
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <path 
        strokeLinecap="round" 
        strokeLinejoin="round" 
        strokeWidth={2} 
        d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" 
      />
    </svg>
  )

  if (variant === 'icon') {
    return (
      <div className="relative">
        <button
          onClick={handleOpenSettings}
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => setShowTooltip(false)}
          className={`${baseClasses} ${variantClasses[variant]} ${className}`}
          aria-label="Cookie-Einstellungen öffnen"
          title="Cookie-Einstellungen"
        >
          <CookieIcon />
        </button>
        
        {showTooltip && (
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 text-xs text-white bg-gray-900 rounded whitespace-nowrap z-50">
            Cookie-Einstellungen
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
          </div>
        )}
      </div>
    )
  }

  if (variant === 'button') {
    return (
      <button
        onClick={handleOpenSettings}
        className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      >
        {showIcon && <CookieIcon />}
        Cookie-Einstellungen
      </button>
    )
  }

  // Default link variant
  return (
    <Link
      href="/cookie-einstellungen"
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
    >
      {showIcon && <CookieIcon />}
      Cookie-Einstellungen
    </Link>
  )
}

/**
 * Cookie consent status indicator
 * Shows current consent status and allows quick access to settings
 */
export const CookieConsentStatus = () => {
  const [consent, setConsent] = useState(() => cookieManager.getConsent())

  const getStatusColor = () => {
    if (!consent.hasConsented) return 'text-yellow-600'
    
    const { preferences } = consent
    const enabledCount = Object.values(preferences).filter(Boolean).length
    
    if (enabledCount === 1) return 'text-red-600' // Only necessary
    if (enabledCount === 4) return 'text-green-600' // All enabled
    return 'text-blue-600' // Partial
  }

  const getStatusText = () => {
    if (!consent.hasConsented) return 'Nicht konfiguriert'
    
    const { preferences } = consent
    const enabledCount = Object.values(preferences).filter(Boolean).length
    
    if (enabledCount === 1) return 'Nur notwendige'
    if (enabledCount === 4) return 'Alle akzeptiert'
    return 'Teilweise konfiguriert'
  }

  return (
    <div className="flex items-center gap-2 text-sm">
      <div className={`w-2 h-2 rounded-full ${getStatusColor().replace('text-', 'bg-')}`}></div>
      <span className={getStatusColor()}>
        Cookies: {getStatusText()}
      </span>
      <CookieSettingsButton variant="link" className="ml-2" showIcon={false} />
    </div>
  )
}

/**
 * Cookie reset button for admin/debug purposes
 */
export const CookieResetButton = ({ className = '' }: { className?: string }) => {
  const handleReset = () => {
    if (confirm('Möchten Sie wirklich alle Cookie-Einstellungen zurücksetzen?')) {
      cookieManager.resetAllConsent()
      window.location.reload()
    }
  }

  return (
    <button
      onClick={handleReset}
      className={`text-xs text-red-600 hover:text-red-800 underline ${className}`}
    >
      Cookie-Einstellungen zurücksetzen
    </button>
  )
}
