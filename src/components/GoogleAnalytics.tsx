'use client'

import { useEffect, useState } from 'react'
import Script from 'next/script'
import { useAnalyticsConsent } from '@/hooks/useCookieConsent'

interface GoogleAnalyticsProps {
  measurementId: string
}

export const GoogleAnalytics = ({ measurementId }: GoogleAnalyticsProps) => {
  const { isAllowed, isLoading } = useAnalyticsConsent()
  const [isInitialized, setIsInitialized] = useState(false)

  // Initialize Google Analytics when consent is given
  useEffect(() => {
    if (!isLoading && isAllowed && !isInitialized) {
      setIsInitialized(true)
    }
  }, [isAllowed, isLoading, isInitialized])

  // Update consent when it changes
  useEffect(() => {
    if (!isLoading && typeof window !== 'undefined') {
      // Wait for gtag to be available
      const checkGtag = () => {
        if ((window as any).gtag) {
          (window as any).gtag('consent', 'update', {
            analytics_storage: isAllowed ? 'granted' : 'denied'
          })
        } else if (isAllowed) {
          // If gtag is not available yet but consent is given, try again in 100ms
          setTimeout(checkGtag, 100)
        }
      }
      checkGtag()
    }
  }, [isAllowed, isLoading])

  // Don't render anything if loading or no consent
  if (isLoading || !isAllowed || !isInitialized) {
    return null
  }

  return (
    <>
      {/* Initialize gtag function first */}
      <Script id="gtag-init" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          window.gtag = gtag;
          gtag('js', new Date());

          // Set default consent state
          gtag('consent', 'default', {
            analytics_storage: 'granted',
            ad_storage: 'denied',
            ad_user_data: 'denied',
            ad_personalization: 'denied',
            wait_for_update: 500,
          });
        `}
      </Script>

      {/* Load Google Analytics script */}
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${measurementId}`}
        strategy="afterInteractive"
        onLoad={() => {
          // Configure Google Analytics after script loads
          if (typeof window !== 'undefined' && (window as any).gtag) {
            (window as any).gtag('config', measurementId, {
              page_title: document.title,
              page_location: window.location.href,
            })
          }
        }}
      />
    </>
  )
}

/**
 * Facebook Pixel component with cookie consent integration
 */
interface FacebookPixelProps {
  pixelId: string
}

export const FacebookPixel = ({ pixelId }: FacebookPixelProps) => {
  const { isAllowed, isLoading } = useAnalyticsConsent() // Using analytics for now, could be marketing

  useEffect(() => {
    if (!isLoading && isAllowed && typeof window !== 'undefined') {
      // Initialize Facebook Pixel
      if (!(window as any).fbq) {
        const fbq = function(...args: any[]) {
          if ((fbq as any).callMethod) {
            (fbq as any).callMethod.apply(fbq, args)
          } else {
            (fbq as any).queue.push(args)
          }
        };
        (fbq as any).push = fbq;
        (fbq as any).loaded = true;
        (fbq as any).version = '2.0';
        (fbq as any).queue = [];
        (window as any).fbq = fbq;
      }

      // Load Facebook Pixel script
      const script = document.createElement('script')
      script.async = true
      script.src = 'https://connect.facebook.net/en_US/fbevents.js'
      document.head.appendChild(script)

      // Initialize pixel
      ;(window as any).fbq('init', pixelId)
      ;(window as any).fbq('track', 'PageView')
    }
  }, [isAllowed, isLoading, pixelId])

  if (isLoading || !isAllowed) {
    return null
  }

  return (
    <Script id="facebook-pixel" strategy="afterInteractive">
      {`
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        
        fbq('init', '${pixelId}');
        fbq('track', 'PageView');
      `}
    </Script>
  )
}

/**
 * Generic tracking script component with cookie consent
 */
interface ConsentTrackingScriptProps {
  category: 'analytics' | 'marketing' | 'functional'
  src?: string
  children?: string
  id: string
}

export const ConsentTrackingScript = ({ 
  category, 
  src, 
  children, 
  id 
}: ConsentTrackingScriptProps) => {
  const { isAllowed, isLoading } = useAnalyticsConsent() // This should be dynamic based on category

  if (isLoading || !isAllowed) {
    return null
  }

  if (src) {
    return <Script src={src} strategy="afterInteractive" id={id} />
  }

  if (children) {
    return (
      <Script id={id} strategy="afterInteractive">
        {children}
      </Script>
    )
  }

  return null
}
