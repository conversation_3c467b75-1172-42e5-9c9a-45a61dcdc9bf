import { cookie<PERSON>ana<PERSON>, <PERSON><PERSON><PERSON>ate<PERSON><PERSON> } from './cookieManager'

/**
 * <PERSON>ript blocker for cookie consent compliance
 * Prevents loading of tracking scripts until consent is given
 */

interface BlockedScript {
  src?: string
  innerHTML?: string
  type: string
  category: CookieCategory
  originalType?: string
}

class CookieScriptBlocker {
  private blockedScripts: Map<Element, BlockedScript> = new Map()
  private observer: MutationObserver | null = null
  private isInitialized = false

  /**
   * Initialize the script blocker
   */
  init() {
    if (this.isInitialized || typeof document === 'undefined') return
    
    this.isInitialized = true
    this.blockExistingScripts()
    this.startObserving()
    this.listenForConsentChanges()
  }

  /**
   * Block existing scripts on page load
   */
  private blockExistingScripts() {
    const scripts = document.querySelectorAll('script')
    scripts.forEach(script => this.processScript(script))
  }

  /**
   * Start observing for new scripts
   */
  private startObserving() {
    this.observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element
            if (element.tagName === 'SCRIPT') {
              this.processScript(element as HTMLScriptElement)
            }
            // Also check for scripts within added elements
            const scripts = element.querySelectorAll?.('script')
            scripts?.forEach(script => this.processScript(script))
          }
        })
      })
    })

    this.observer.observe(document.documentElement, {
      childList: true,
      subtree: true
    })
  }

  /**
   * Process a script element and block if necessary
   */
  private processScript(script: HTMLScriptElement) {
    const category = this.categorizeScript(script)
    
    if (category && category !== 'necessary') {
      const isAllowed = cookieManager.isCategoryAllowed(category)
      
      if (!isAllowed) {
        this.blockScript(script, category)
      }
    }
  }

  /**
   * Categorize a script based on its source or content
   */
  private categorizeScript(script: HTMLScriptElement): CookieCategory | null {
    const src = script.src?.toLowerCase() || ''
    const content = script.innerHTML?.toLowerCase() || ''
    
    // Google Analytics
    if (src.includes('googletagmanager.com') || 
        src.includes('google-analytics.com') ||
        content.includes('gtag') ||
        content.includes('ga(') ||
        content.includes('_gaq')) {
      return 'analytics'
    }
    
    // Facebook Pixel
    if (src.includes('connect.facebook.net') ||
        content.includes('fbq') ||
        content.includes('facebook')) {
      return 'marketing'
    }
    
    // Other marketing/advertising scripts
    if (src.includes('doubleclick.net') ||
        src.includes('googlesyndication.com') ||
        src.includes('adsystem.com') ||
        src.includes('amazon-adsystem.com') ||
        content.includes('adsense')) {
      return 'marketing'
    }
    
    // Analytics services
    if (src.includes('hotjar.com') ||
        src.includes('mixpanel.com') ||
        src.includes('segment.com') ||
        src.includes('amplitude.com')) {
      return 'analytics'
    }
    
    // Chat/Support widgets (functional)
    if (src.includes('zendesk.com') ||
        src.includes('intercom.io') ||
        src.includes('crisp.chat') ||
        src.includes('tawk.to')) {
      return 'functional'
    }
    
    return null
  }

  /**
   * Block a script by changing its type
   */
  private blockScript(script: HTMLScriptElement, category: CookieCategory) {
    const blockedScript: BlockedScript = {
      src: script.src,
      innerHTML: script.innerHTML,
      type: script.type || 'text/javascript',
      category,
      originalType: script.type
    }
    
    // Store the blocked script info
    this.blockedScripts.set(script, blockedScript)
    
    // Change script type to prevent execution
    script.type = 'text/plain'
    script.setAttribute('data-cookie-category', category)
    script.setAttribute('data-blocked', 'true')
    
    // Remove src to prevent loading
    if (script.src) {
      script.removeAttribute('src')
    }
  }

  /**
   * Unblock scripts for a specific category
   */
  private unblockScripts(category: CookieCategory) {
    this.blockedScripts.forEach((blockedScript, script) => {
      if (blockedScript.category === category) {
        this.unblockScript(script as HTMLScriptElement, blockedScript)
      }
    })
  }

  /**
   * Unblock a specific script
   */
  private unblockScript(script: HTMLScriptElement, blockedScript: BlockedScript) {
    // Create a new script element to replace the blocked one
    const newScript = document.createElement('script')
    
    // Copy attributes
    Array.from(script.attributes).forEach(attr => {
      if (attr.name !== 'type' && 
          attr.name !== 'data-cookie-category' && 
          attr.name !== 'data-blocked') {
        newScript.setAttribute(attr.name, attr.value)
      }
    })
    
    // Restore original type
    newScript.type = blockedScript.originalType || blockedScript.type
    
    // Restore src or innerHTML
    if (blockedScript.src) {
      newScript.src = blockedScript.src
    }
    if (blockedScript.innerHTML) {
      newScript.innerHTML = blockedScript.innerHTML
    }
    
    // Replace the blocked script
    script.parentNode?.replaceChild(newScript, script)
    
    // Remove from blocked scripts map
    this.blockedScripts.delete(script)
  }

  /**
   * Listen for consent changes
   */
  private listenForConsentChanges() {
    window.addEventListener('cookieConsentChanged', (event: CustomEvent) => {
      const consent = event.detail
      
      // Unblock scripts for enabled categories
      Object.entries(consent.preferences).forEach(([category, enabled]) => {
        if (enabled && category !== 'necessary') {
          this.unblockScripts(category as CookieCategory)
        }
      })
    })
  }

  /**
   * Manually unblock all scripts (for testing)
   */
  unblockAll() {
    this.blockedScripts.forEach((blockedScript, script) => {
      this.unblockScript(script as HTMLScriptElement, blockedScript)
    })
  }

  /**
   * Get blocked scripts count by category
   */
  getBlockedScriptsCount(): Record<CookieCategory, number> {
    const counts: Record<CookieCategory, number> = {
      necessary: 0,
      functional: 0,
      analytics: 0,
      marketing: 0
    }
    
    this.blockedScripts.forEach(script => {
      counts[script.category]++
    })
    
    return counts
  }

  /**
   * Destroy the script blocker
   */
  destroy() {
    if (this.observer) {
      this.observer.disconnect()
      this.observer = null
    }
    this.blockedScripts.clear()
    this.isInitialized = false
  }
}

// Export singleton instance
export const cookieScriptBlocker = new CookieScriptBlocker()

// Auto-initialize when DOM is ready
if (typeof document !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      cookieScriptBlocker.init()
    })
  } else {
    cookieScriptBlocker.init()
  }
}
