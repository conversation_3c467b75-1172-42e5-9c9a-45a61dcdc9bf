export type CookieCategory = 'necessary' | 'functional' | 'analytics' | 'marketing'

export interface CookieConsent {
  hasConsented: boolean
  consentDate?: Date
  preferences: Record<CookieCategory, boolean>
  version: string
}

export interface CookieInfo {
  name: string
  category: CookieCategory
  purpose: string
  duration: string
  provider: string
  essential: boolean
}

// Current consent version - increment when cookie policy changes
const CONSENT_VERSION = '1.0'
const CONSENT_STORAGE_KEY = 'db_performance_cookie_consent'

// Cookie definitions according to nDSG requirements
export const COOKIE_DEFINITIONS: CookieInfo[] = [
  // Necessary Cookies
  {
    name: 'cookie_consent',
    category: 'necessary',
    purpose: 'Speichert Ihre Cookie-Einstellungen und Präferenzen',
    duration: '1 Jahr',
    provider: 'DB-Performance',
    essential: true,
  },
  {
    name: 'session_id',
    category: 'necessary',
    purpose: 'Technische Session-Verwaltung für sichere Webseitennutzung',
    duration: 'Session',
    provider: 'DB-Performance',
    essential: true,
  },
  
  // Functional Cookies
  {
    name: 'user_preferences',
    category: 'functional',
    purpose: 'Speichert Ihre Benutzereinstellungen und Präferenzen',
    duration: '6 Monate',
    provider: 'DB-Performance',
    essential: false,
  },
  {
    name: 'language_preference',
    category: 'functional',
    purpose: 'Speichert Ihre bevorzugte Sprache',
    duration: '1 Jahr',
    provider: 'DB-Performance',
    essential: false,
  },
  
  // Analytics Cookies
  {
    name: '_ga',
    category: 'analytics',
    purpose: 'Unterscheidet Benutzer für Google Analytics',
    duration: '2 Jahre',
    provider: 'Google',
    essential: false,
  },
  {
    name: '_ga_*',
    category: 'analytics',
    purpose: 'Sammelt Daten über Website-Nutzung für Google Analytics',
    duration: '2 Jahre',
    provider: 'Google',
    essential: false,
  },
  {
    name: '_gid',
    category: 'analytics',
    purpose: 'Unterscheidet Benutzer für Google Analytics',
    duration: '24 Stunden',
    provider: 'Google',
    essential: false,
  },
  
  // Marketing Cookies
  {
    name: '_fbp',
    category: 'marketing',
    purpose: 'Facebook Pixel für Werbezwecke und Conversion-Tracking',
    duration: '3 Monate',
    provider: 'Meta/Facebook',
    essential: false,
  },
  {
    name: '_fbc',
    category: 'marketing',
    purpose: 'Facebook Click-ID für Conversion-Tracking',
    duration: '3 Monate',
    provider: 'Meta/Facebook',
    essential: false,
  },
]

class CookieManager {
  private consent: CookieConsent | null = null

  constructor() {
    if (typeof window !== 'undefined') {
      this.loadConsent()
    }
  }

  /**
   * Load consent from localStorage
   */
  private loadConsent(): void {
    try {
      const stored = localStorage.getItem(CONSENT_STORAGE_KEY)
      if (stored) {
        const parsed = JSON.parse(stored)
        // Check if consent version matches current version
        if (parsed.version === CONSENT_VERSION) {
          this.consent = {
            ...parsed,
            consentDate: parsed.consentDate ? new Date(parsed.consentDate) : undefined,
          }
        } else {
          // Version mismatch - reset consent
          this.resetConsent()
        }
      }
    } catch (error) {
      console.error('Error loading cookie consent:', error)
      this.resetConsent()
    }
  }

  /**
   * Save consent to localStorage
   */
  private saveConsent(): void {
    if (!this.consent) return

    try {
      const consentString = JSON.stringify(this.consent)
      localStorage.setItem(CONSENT_STORAGE_KEY, consentString)
      console.log('Cookie consent saved to localStorage:', this.consent)
    } catch (error) {
      console.error('Error saving cookie consent:', error)
    }
  }

  /**
   * Reset consent to default state
   */
  private resetConsent(): void {
    this.consent = null
    try {
      localStorage.removeItem(CONSENT_STORAGE_KEY)
    } catch (error) {
      console.error('Error removing cookie consent:', error)
    }
  }

  /**
   * Get current consent state
   */
  getConsent(): CookieConsent {
    if (!this.consent) {
      return {
        hasConsented: false,
        preferences: {
          necessary: true,
          functional: false,
          analytics: false,
          marketing: false,
        },
        version: CONSENT_VERSION,
      }
    }
    return this.consent
  }

  /**
   * Set user consent preferences
   */
  setConsent(preferences: Record<CookieCategory, boolean>): void {
    this.consent = {
      hasConsented: true,
      consentDate: new Date(),
      preferences: {
        necessary: true, // Always true
        ...preferences,
      },
      version: CONSENT_VERSION,
    }
    
    this.saveConsent()
    this.applyCookieSettings()
    
    // Dispatch custom event for other components to listen to
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('cookieConsentChanged', {
        detail: this.consent
      }))
    }
  }

  /**
   * Check if a specific cookie category is allowed
   */
  isCategoryAllowed(category: CookieCategory): boolean {
    const consent = this.getConsent()
    return consent.preferences[category] || false
  }

  /**
   * Check if a specific cookie is allowed
   */
  isCookieAllowed(cookieName: string): boolean {
    const cookieInfo = COOKIE_DEFINITIONS.find(c => c.name === cookieName)
    if (!cookieInfo) return false
    
    if (cookieInfo.essential) return true
    
    return this.isCategoryAllowed(cookieInfo.category)
  }

  /**
   * Apply cookie settings by removing disallowed cookies
   */
  private applyCookieSettings(): void {
    if (typeof document === 'undefined') return

    const consent = this.getConsent()
    
    // Remove cookies for disabled categories
    COOKIE_DEFINITIONS.forEach(cookieInfo => {
      if (!cookieInfo.essential && !consent.preferences[cookieInfo.category]) {
        this.deleteCookie(cookieInfo.name)
      }
    })

    // Handle Google Analytics
    if (!consent.preferences.analytics) {
      this.deleteCookie('_ga')
      this.deleteCookie('_gid')
      // Delete all _ga_* cookies
      document.cookie.split(';').forEach(cookie => {
        const name = cookie.split('=')[0].trim()
        if (name.startsWith('_ga_')) {
          this.deleteCookie(name)
        }
      })
      
      // Disable Google Analytics if gtag is available
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('consent', 'update', {
          analytics_storage: 'denied'
        })
      }
    } else {
      // Enable Google Analytics if gtag is available
      if (typeof window !== 'undefined' && (window as any).gtag) {
        (window as any).gtag('consent', 'update', {
          analytics_storage: 'granted'
        })
      }
    }

    // Handle Facebook Pixel
    if (!consent.preferences.marketing) {
      this.deleteCookie('_fbp')
      this.deleteCookie('_fbc')
    }
  }

  /**
   * Delete a specific cookie
   */
  private deleteCookie(name: string): void {
    if (typeof document === 'undefined') return
    
    // Delete for current domain
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=${window.location.hostname}`
    
    // Also try with leading dot for subdomain cookies
    const domain = window.location.hostname
    if (domain.includes('.')) {
      const rootDomain = '.' + domain.split('.').slice(-2).join('.')
      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=${rootDomain}`
    }
  }

  /**
   * Get cookies by category
   */
  getCookiesByCategory(category: CookieCategory): CookieInfo[] {
    return COOKIE_DEFINITIONS.filter(cookie => cookie.category === category)
  }

  /**
   * Get all cookie definitions
   */
  getAllCookies(): CookieInfo[] {
    return COOKIE_DEFINITIONS
  }

  /**
   * Reset all consent and cookies
   */
  resetAllConsent(): void {
    // Remove all non-essential cookies
    COOKIE_DEFINITIONS.forEach(cookieInfo => {
      if (!cookieInfo.essential) {
        this.deleteCookie(cookieInfo.name)
      }
    })
    
    this.resetConsent()
    
    // Dispatch reset event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('cookieConsentReset'))
    }
  }

  /**
   * Check if consent is required (for banner display)
   */
  isConsentRequired(): boolean {
    return !this.getConsent().hasConsented
  }
}

// Export singleton instance
export const cookieManager = new CookieManager()

// Utility function to check if cookies are allowed in components
export const useCookieConsent = () => {
  return {
    isAllowed: (category: CookieCategory) => cookieManager.isCategoryAllowed(category),
    isCookieAllowed: (cookieName: string) => cookieManager.isCookieAllowed(cookieName),
    getConsent: () => cookieManager.getConsent(),
    setConsent: (preferences: Record<CookieCategory, boolean>) => cookieManager.setConsent(preferences),
  }
}
