'use client'

import { useState, useEffect } from 'react'
import { cookie<PERSON>anager, CookieCategory, CookieConsent } from '@/lib/cookieManager'

export interface UseCookieConsentReturn {
  consent: CookieConsent
  isLoading: boolean
  isAllowed: (category: CookieCategory) => boolean
  isCookieAllowed: (cookieName: string) => boolean
  setConsent: (preferences: Record<CookieCategory, boolean>) => void
  resetConsent: () => void
  showBanner: boolean
}

/**
 * Hook for managing cookie consent state
 * Provides reactive access to cookie consent status and preferences
 */
export const useCookieConsent = (): UseCookieConsentReturn => {
  const [consent, setConsentState] = useState<CookieConsent>(() => {
    // Initialize with default state
    return {
      hasConsented: false,
      preferences: {
        necessary: true,
        functional: false,
        analytics: false,
        marketing: false,
      },
      version: '1.0',
    }
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Load initial consent state
    const currentConsent = cookieManager.getConsent()
    setConsentState(currentConsent)
    setIsLoading(false)

    // Listen for consent changes
    const handleConsentChange = (event: CustomEvent) => {
      setConsentState(event.detail)
    }

    const handleConsentReset = () => {
      setConsentState({
        hasConsented: false,
        preferences: {
          necessary: true,
          functional: false,
          analytics: false,
          marketing: false,
        },
        version: '1.0',
      })
    }

    window.addEventListener('cookieConsentChanged', handleConsentChange as EventListener)
    window.addEventListener('cookieConsentReset', handleConsentReset)

    return () => {
      window.removeEventListener('cookieConsentChanged', handleConsentChange as EventListener)
      window.removeEventListener('cookieConsentReset', handleConsentReset)
    }
  }, [])

  const isAllowed = (category: CookieCategory): boolean => {
    return cookieManager.isCategoryAllowed(category)
  }

  const isCookieAllowed = (cookieName: string): boolean => {
    return cookieManager.isCookieAllowed(cookieName)
  }

  const setConsent = (preferences: Record<CookieCategory, boolean>): void => {
    cookieManager.setConsent(preferences)
  }

  const resetConsent = (): void => {
    cookieManager.resetAllConsent()
  }

  const showBanner = !consent.hasConsented

  return {
    consent,
    isLoading,
    isAllowed,
    isCookieAllowed,
    setConsent,
    resetConsent,
    showBanner,
  }
}

/**
 * Hook for checking if a specific cookie category is allowed
 * Useful for conditional rendering of tracking scripts
 */
export const useCookieCategory = (category: CookieCategory) => {
  const { isAllowed, isLoading } = useCookieConsent()
  
  return {
    isAllowed: isAllowed(category),
    isLoading,
  }
}

/**
 * Hook for Google Analytics consent
 */
export const useAnalyticsConsent = () => {
  const { isAllowed, isLoading } = useCookieCategory('analytics')
  
  useEffect(() => {
    if (!isLoading && typeof window !== 'undefined' && (window as any).gtag) {
      // Update Google Analytics consent
      (window as any).gtag('consent', 'update', {
        analytics_storage: isAllowed ? 'granted' : 'denied'
      })
    }
  }, [isAllowed, isLoading])
  
  return { isAllowed, isLoading }
}

/**
 * Hook for Marketing/Advertising consent
 */
export const useMarketingConsent = () => {
  const { isAllowed, isLoading } = useCookieCategory('marketing')
  
  useEffect(() => {
    if (!isLoading && typeof window !== 'undefined') {
      // Update Google Ads consent if gtag is available
      if ((window as any).gtag) {
        (window as any).gtag('consent', 'update', {
          ad_storage: isAllowed ? 'granted' : 'denied',
          ad_user_data: isAllowed ? 'granted' : 'denied',
          ad_personalization: isAllowed ? 'granted' : 'denied'
        })
      }
      
      // Handle Facebook Pixel
      if ((window as any).fbq) {
        if (isAllowed) {
          (window as any).fbq('consent', 'grant')
        } else {
          (window as any).fbq('consent', 'revoke')
        }
      }
    }
  }, [isAllowed, isLoading])
  
  return { isAllowed, isLoading }
}

/**
 * Hook for functional cookies consent
 */
export const useFunctionalConsent = () => {
  return useCookieCategory('functional')
}
