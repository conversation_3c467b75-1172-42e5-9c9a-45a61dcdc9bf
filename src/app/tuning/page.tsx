import dynamic from 'next/dynamic';
import { strings } from '../../strings';

const TuningConfigurator = dynamic(() => import('@/components/TuningConfigurator'), {
  loading: () => (
    <div className="flex items-center justify-center py-12">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
      <span className="ml-3 text-gray-600">Lade Konfigurator...</span>
    </div>
  ),
});

export const metadata = {
  title: `${strings.tuning.title} | ${strings.home.title}`,
  description: strings.tuning.subtitle,
};

export default function TuningPage() {
  return <TuningConfigurator />;
}