import { sanityService } from '@/sanity/services/SanityService'
import { PortableText } from '@portabletext/react'

interface LegalPageData {
  title: string
  content: Array<{
    children?: Array<{
      marks?: Array<string>
      text?: string
      _type: 'span'
      _key: string
    }>
    style?: 'normal' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'blockquote'
    listItem?: 'bullet' | 'number'
    markDefs?: Array<{
      href?: string
      _type: 'link'
      _key: string
    }>
    level?: number
    _type: 'block'
    _key: string
  }>
}

async function getLegalPage(slug: string): Promise<LegalPageData | null> {
  return sanityService.getLegalPage(slug) as Promise<LegalPageData | null>
}

export default async function AGBPage() {
  const data = await getLegalPage('agb')
  
  if (!data) {
    return (
      <div className="container mx-auto px-6 py-16">
        <h1 className="text-3xl font-bold mb-8">Seite nicht gefunden</h1>
        <p>Die angeforderte Seite konnte nicht gefunden werden.</p>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-6 py-16">
      <div className="prose prose-lg max-w-none">
        <PortableText value={data.content} />
      </div>
    </div>
  )
}

export async function generateMetadata() {
  const data = await getLegalPage('agb')
  
  return {
    title: data?.title || 'Allgemeine Geschäftsbedingungen',
    description: 'AGB von DB-Performance Garage Bytyci',
  }
} 