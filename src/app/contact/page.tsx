import { strings } from '@/strings'
import { WhatsAppButton } from '@/components/WhatsAppButton'

const contactData = {
  heroTitle: 'Kontakt',
  heroSubtitle: 'Nehmen Sie Kontakt mit uns auf',
  heroDescription: 'Wir sind für Sie da - per Telefon, E-Mail oder WhatsApp',

  contactInfo: {
    title: 'Kontaktinformationen',
    address: {
      title: 'Adresse',
      street: strings.contact.company.address,
      city: strings.contact.company.city,
      country: strings.contact.company.country
    },
    phone: {
      title: 'Telefon',
      number: strings.contact.company.phone
    },
    email: {
      title: 'E-Mail',
      address: strings.contact.company.email
    },
    hours: {
      title: 'Öffnungszeiten',
      weekdays: 'Mo - Fr: 08:00 - 18:00',
      saturday: 'Sa: 09:00 - 16:00',
      sunday: 'So: Geschlossen'
    }
  },

  map: {
    title: 'Standort',
    description: 'Besuchen Sie uns in unserer Werkstatt in Leuggern',
    // Koordinaten für Leuggern, Schweiz (Stauseestrasse 1, 5316 Leuggern)
    latitude: 47.5880775,
    longitude: 8.2203582,
    zoom: 15
  },
  

  
  cta: {
    title: 'Bereit für Ihr Projekt?',
    description: 'Kontaktieren Sie uns noch heute für eine unverbindliche Beratung',
    whatsappText: 'WhatsApp Chat',
    emailText: 'E-Mail senden',
    phoneText: 'Anrufen'
  }
}

export default function ContactPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative flex flex-col items-center justify-center min-h-[50vh] bg-cover bg-center bg-no-repeat text-white" style={{
        backgroundImage: 'linear-gradient(rgba(58, 58, 58, 0.8) 0%, rgba(58, 58, 58, 0.9) 100%), url("/DBP_M3_Lucas.jpeg")',
        filter: 'grayscale(100%)'
      }}>
        <div className="text-center p-8">
          <h1 className="text-5xl font-extrabold leading-tight tracking-tighter sm:text-6xl md:text-7xl">
            {contactData.heroTitle}
          </h1>
          <p className="mt-4 max-w-2xl mx-auto text-lg text-gray-300">
            {contactData.heroSubtitle}
          </p>
          <p className="mt-2 max-w-2xl mx-auto text-base text-gray-400">
            {contactData.heroDescription}
          </p>
        </div>
      </section>



      {/* Contact Buttons and Map Section */}
      <section className="py-8 sm:py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
            {/* Contact Buttons */}
            <div className="grid grid-cols-2 gap-4">
              {/* Phone */}
              <a
                href={`tel:${contactData.contactInfo.phone.number}`}
                className="bg-white rounded-lg shadow-md p-6 text-center hover:bg-gray-50 transition-colors"
              >
                <div className="bg-accent-red text-white rounded-full p-4 mb-4 mx-auto w-16 h-16 flex items-center justify-center">
                  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-anthracite mb-2">{contactData.contactInfo.phone.title}</h3>
                <p className="text-gray-600">{contactData.contactInfo.phone.number}</p>
              </a>

              {/* Email */}
              <a
                href={`mailto:${contactData.contactInfo.email.address}`}
                className="bg-white rounded-lg shadow-md p-6 text-center hover:bg-gray-50 transition-colors"
              >
                <div className="bg-accent-red text-white rounded-full p-4 mb-4 mx-auto w-16 h-16 flex items-center justify-center">
                  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-anthracite mb-2">{contactData.contactInfo.email.title}</h3>
                <p className="text-gray-600">{contactData.contactInfo.email.address}</p>
              </a>

              {/* WhatsApp */}
              <div className="bg-white rounded-lg shadow-md p-6 text-center">
                <div className="bg-green-600 text-white rounded-full p-4 mb-4 mx-auto w-16 h-16 flex items-center justify-center">
                  <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-anthracite mb-2">WhatsApp</h3>
                <WhatsAppButton
                  className="text-green-600 hover:text-green-700 font-medium"
                  message={strings.contact.whatsapp.defaultMessage}
                  phoneNumber={strings.contact.company.whatsapp}
                >
                  Nachricht senden
                </WhatsAppButton>
              </div>

              {/* Route planen */}
              <a
                href={`https://www.google.com/maps/dir/?api=1&destination=${contactData.map.latitude},${contactData.map.longitude}&destination_place_id=ChIJ&travelmode=driving`}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white rounded-lg shadow-md p-6 text-center hover:bg-gray-50 transition-colors"
              >
                <div className="bg-accent-red text-white rounded-full p-4 mb-4 mx-auto w-16 h-16 flex items-center justify-center">
                  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-anthracite mb-2">Route</h3>
                <p className="text-gray-600">Route planen</p>
              </a>
            </div>

            {/* Map */}
            <div className="bg-gray-100 rounded-lg overflow-hidden shadow-lg">
              <iframe
                src={`https://www.openstreetmap.org/export/embed.html?bbox=${contactData.map.longitude - 0.01},${contactData.map.latitude - 0.01},${contactData.map.longitude + 0.01},${contactData.map.latitude + 0.01}&layer=mapnik&marker=${contactData.map.latitude},${contactData.map.longitude}`}
                width="100%"
                height="400"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="DB-Performance Standort"
                className="w-full h-96"
              ></iframe>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-anthracite text-white py-8 sm:py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-extrabold sm:text-4xl">
            {contactData.cta.title}
          </h2>
          <p className="mt-4 text-lg text-gray-300">
            {contactData.cta.description}
          </p>
          <div className="mt-4 flex flex-col sm:flex-row gap-4 justify-center">
            <WhatsAppButton
              className="flex min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-md h-12 px-6 bg-green-600 text-white text-base font-bold shadow-xl transition-transform hover:scale-105"
              message={strings.contact.whatsapp.defaultMessage}
              phoneNumber={strings.contact.company.whatsapp}
            >
              {contactData.cta.whatsappText}
            </WhatsAppButton>
            
            <a
              href={`mailto:${strings.contact.company.email}`}
              className="flex min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-md h-12 px-6 bg-accent-red text-white text-base font-bold shadow-xl transition-transform hover:scale-105"
            >
              {contactData.cta.emailText}
            </a>

            <a
              href={`tel:${strings.contact.company.phone}`}
              className="flex min-w-[84px] cursor-pointer items-center justify-center overflow-hidden rounded-md h-12 px-6 bg-anthracite border-2 border-white text-white text-base font-bold shadow-xl transition-transform hover:scale-105"
            >
              {contactData.cta.phoneText}
            </a>
          </div>
        </div>
      </section>
    </div>
  )
}
