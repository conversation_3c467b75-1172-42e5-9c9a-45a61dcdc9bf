import { createClient } from '@sanity/client'

const projectId = 'uzktk1gg'
const dataset = 'development'
const apiVersion = '2024-01-01'

async function testSanityConnection() {
  console.log('🔍 Testing Sanity connection...')
  console.log(`Project ID: ${projectId}`)
  console.log(`Dataset: ${dataset}`)
  console.log(`API Version: ${apiVersion}`)

  // Test without token first (public data)
  console.log('\n📡 Testing connection without token (public data)...')
  
  const publicClient = createClient({
    projectId,
    dataset,
    apiVersion,
    useCdn: false,
  })

  try {
    const result = await publicClient.fetch('*[_type == "legalPage"][0..2]{_id, title, slug}')
    console.log('✅ Public connection successful!')
    console.log('📄 Found legal pages:', result?.length || 0)
    if (result && result.length > 0) {
      result.forEach(page => {
        console.log(`   - ${page.title} (${page.slug?.current})`)
      })
    }
  } catch (error) {
    console.error('❌ Public connection failed:', error.message)
  }

  // Test with token
  const token = 'skWd17eHPh2Xn8PHQFsv8yZEWQBcmzjvWGlqNlE9trX3czS146WY9D4u348SYUv81nBNdTxNe4oNhoDy0YYoBI2jcARk03IghSmiXqKKaVlsjTJky1L5j6gE6UgNwCIOAxqN0Wg0AmpbIkMmJge9vwnfUYyd90fNgkLkAHkGNq4zA5SYfTRq'
  
  console.log('\n🔐 Testing connection with token...')
  
  const tokenClient = createClient({
    projectId,
    dataset,
    apiVersion,
    token,
    useCdn: false,
  })

  try {
    const result = await tokenClient.fetch('*[_type == "siteSettings"][0]{siteName, navigation, footer}')
    console.log('✅ Token connection successful!')
    console.log('⚙️ Site settings found:', !!result)
    if (result) {
      console.log(`   Site Name: ${result.siteName || 'Not set'}`)
      console.log(`   Navigation: ${result.navigation ? 'Configured' : 'Not configured'}`)
      console.log(`   Footer: ${result.footer ? 'Configured' : 'Not configured'}`)
    }
  } catch (error) {
    console.error('❌ Token connection failed:', error.message)
    console.log('💡 This might be normal if the token has limited permissions')
  }

  console.log('\n🎯 Testing specific queries...')
  
  try {
    const impressum = await publicClient.fetch('*[_type == "legalPage" && slug.current == "impressum"][0]{title, content[0].children[0].text}')
    console.log('✅ Impressum query successful!')
    console.log(`   Title: ${impressum?.title || 'Not found'}`)
    console.log(`   First text: ${impressum?.content?.[0]?.children?.[0]?.text?.substring(0, 50) || 'No content'}...`)
  } catch (error) {
    console.error('❌ Impressum query failed:', error.message)
  }
}

testSanityConnection()
