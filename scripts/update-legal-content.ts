import { createClient, SanityClient } from 'next-sanity';
import { config } from 'dotenv';

// Load environment variables from .env.local
config({ path: '.env.local' });

const client: SanityClient = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
  apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION || '2024-01-01',
  useCdn: false,
  token: process.env.SANITY_API_WRITE_TOKEN, // Ensure you have a write token
});

// --- Final Legal Content ---

const impressumContent = [
  {
    _type: 'block',
    style: 'h3',
    _key: 'resp_header',
    markDefs: [],
    children: [{ _type: 'span', marks: ['strong'], text: 'Verantwortlich' }],
  },
  {
    _type: 'block',
    style: 'normal',
    _key: 'company_name',
    markDefs: [],
    children: [{ _type: 'span', marks: [], text: 'DB-Performance Garage Bytyci' }],
  },
  {
    _type: 'block',
    style: 'normal',
    _key: 'address_line_1',
    markDefs: [],
    children: [{ _type: 'span', marks: [], text: 'Stauseestrasse 1' }],
  },
  {
    _type: 'block',
    style: 'normal',
    _key: 'address_line_2',
    markDefs: [],
    children: [{ _type: 'span', marks: [], text: '5316 Leuggern' }],
  },
  {
    _type: 'block',
    style: 'normal',
    _key: 'address_line_3',
    markDefs: [],
    children: [{ _type: 'span', marks: [], text: 'Schweiz' }],
  },
  {
    _type: 'block',
    style: 'h3',
    _key: 'contact_header',
    markDefs: [],
    children: [{ _type: 'span', marks: ['strong'], text: 'Kontakt' }],
  },
  {
    _type: 'block',
    style: 'normal',
    _key: 'email',
    markDefs: [],
    children: [{ _type: 'span', marks: [], text: 'E-Mail: [IHRE E-MAIL-ADRESSE HIER EINFÜGEN]' }],
  },
  {
    _type: 'block',
    style: 'h3',
    _key: 'uid_header',
    markDefs: [],
    children: [{ _type: 'span', marks: ['strong'], text: 'Unternehmens-Identifikationsnummer (UID)' }],
  },
  {
    _type: 'block',
    style: 'normal',
    _key: 'uid_value',
    markDefs: [],
    children: [{ _type: 'span', marks: [], text: 'CHE-149.623.444' }],
  },
];

const datenschutzContent = [
  {
    _type: 'block',
    style: 'h2',
    _key: 'main_header',
    markDefs: [],
    children: [{ _type: 'span', marks: [], text: 'Datenschutzerklärung' }],
  },
  {
    _type: 'block',
    style: 'normal',
    _key: 'intro',
    markDefs: [],
    children: [
      {
        _type: 'span',
        marks: [],
        text: 'Diese Datenschutzerklärung klärt Sie über die Art, den Umfang und Zweck der Verarbeitung von personenbezogenen Daten (nachfolgend „Daten“) innerhalb unseres Onlineangebotes auf.',
      },
    ],
  },
  {
    _type: 'block',
    style: 'h3',
    _key: 'resp_header_ds',
    markDefs: [],
    children: [{ _type: 'span', marks: ['strong'], text: 'Verantwortlicher' }],
  },
  {
    _type: 'block',
    style: 'normal',
    _key: 'resp_details_ds_1',
    markDefs: [],
    children: [{ _type: 'span', marks: [], text: 'DB-Performance Garage Bytyci' }],
  },
  {
    _type: 'block',
    style: 'normal',
    _key: 'resp_details_ds_2',
    markDefs: [],
    children: [{ _type: 'span', marks: [], text: 'Stauseestrasse 1' }],
  },
  {
    _type: 'block',
    style: 'normal',
    _key: 'resp_details_ds_3',
    markDefs: [],
    children: [{ _type: 'span', marks: [], text: '5316 Leuggern' }],
  },
  {
    _type: 'block',
    style: 'normal',
    _key: 'resp_details_ds_4',
    markDefs: [],
    children: [{ _type: 'span', marks: [], text: 'Schweiz' }],
  },
  {
    _type: 'block',
    style: 'normal',
    _key: 'resp_details_ds_5',
    markDefs: [],
    children: [{ _type: 'span', marks: [], text: 'E-Mail: [IHRE E-MAIL-ADRESSE HIER EINFÜGEN]' }],
  },
  {
    _type: 'block',
    style: 'h3',
    _key: 'data_types_header',
    markDefs: [],
    children: [{ _type: 'span', marks: ['strong'], text: 'Arten der verarbeiteten Daten' }],
  },
  {
    _type: 'block',
    style: 'normal',
    listItem: 'bullet',
    level: 1,
    _key: 'data_type_1',
    markDefs: [],
    children: [
      { _type: 'span', marks: ['strong'], text: 'Nutzungsdaten:' },
      { _type: 'span', marks: [], text: ' Besuchte Webseiten, Zugriffszeiten, Meta- und Kommunikationsdaten wie IP-Adressen.' },
    ],
  },
  {
    _type: 'block',
    style: 'normal',
    listItem: 'bullet',
    level: 1,
    _key: 'data_type_2',
    markDefs: [],
    children: [
      { _type: 'span', marks: ['strong'], text: 'Kontaktdaten:' },
      { _type: 'span', marks: [], text: ' Von Ihnen übermittelte Daten bei einer Kontaktaufnahme (z.B. Name, E-Mail).' },
    ],
  },
  {
    _type: 'block',
    style: 'normal',
    listItem: 'bullet',
    level: 1,
    _key: 'data_type_3',
    markDefs: [],
    children: [
      { _type: 'span', marks: ['strong'], text: 'Inhaltsdaten:' },
      { _type: 'span', marks: [], text: ' Daten, die Sie im Tuning-Konfigurator eingeben.' },
    ],
  },
  {
    _type: 'block',
    style: 'h3',
    _key: 'processing_purpose_header',
    markDefs: [],
    children: [{ _type: 'span', marks: ['strong'], text: 'Zweck der Verarbeitung' }],
  },
  {
    _type: 'block',
    style: 'normal',
    listItem: 'bullet',
    level: 1,
    _key: 'purpose_1',
    markDefs: [],
    children: [
      { _type: 'span', marks: ['strong'], text: 'Zurverfügungstellung der Webseite:' },
      { _type: 'span', marks: [], text: ' Sicherstellung eines sicheren und stabilen Betriebs.' },
    ],
  },
  {
    _type: 'block',
    style: 'normal',
    listItem: 'bullet',
    level: 1,
    _key: 'purpose_2',
    markDefs: [],
    children: [
      { _type: 'span', marks: ['strong'], text: 'Beantwortung von Kontaktanfragen:' },
      { _type: 'span', marks: [], text: ' Bearbeitung Ihrer Anliegen.' },
    ],
  },
  {
    _type: 'block',
    style: 'normal',
    listItem: 'bullet',
    level: 1,
    _key: 'purpose_3',
    markDefs: [],
    children: [
      { _type: 'span', marks: ['strong'], text: 'Sicherheitsmassnahmen:' },
      { _type: 'span', marks: [], text: ' Schutz vor Missbrauch.' },
    ],
  },
  {
    _type: 'block',
    style: 'h3',
    _key: 'services_header',
    markDefs: [],
    children: [{ _type: 'span', marks: ['strong'], text: 'Eingesetzte Dienste und Drittanbieter' }],
  },
  {
    _type: 'block',
    style: 'normal',
    _key: 'services_intro',
    markDefs: [],
    children: [
      {
        _type: 'span',
        marks: [],
        text: 'Für den Betrieb dieser Webseite arbeiten wir mit spezialisierten Dienstleistern zusammen:',
      },
    ],
  },
  {
    _type: 'block',
    style: 'normal',
    listItem: 'bullet',
    level: 1,
    _key: 'service_vercel',
    markDefs: [
      {
        _key: 'vercel_link',
        _type: 'link',
        href: 'https://vercel.com/legal/privacy-policy',
      },
    ],
    children: [
      { _type: 'span', marks: ['strong'], text: 'Hosting via Vercel:' },
      { _type: 'span', marks: [], text: ' Unsere Webseite wird bei ' },
      { _type: 'span', marks: ['strong'], text: 'Vercel Inc.' },
      { _type: 'span', marks: [], text: ' (340 S Lemon Ave #4133, Walnut, CA 91789, USA) gehostet. Beim Besuch der Webseite werden Nutzungsdaten auf den Servern von Vercel verarbeitet. Die Datenübermittlung in die USA ist durch Standardvertragsklauseln abgesichert, die ein angemessenes Datenschutzniveau gewährleisten. Details finden Sie in der ' },
      { _type: 'span', marks: ['vercel_link'], text: 'Datenschutzerklärung von Vercel' },
      { _type: 'span', marks: [], text: '.' },
    ],
  },
  {
    _type: 'block',
    style: 'normal',
    listItem: 'bullet',
    level: 1,
    _key: 'service_supabase',
    markDefs: [
      {
        _key: 'supabase_link',
        _type: 'link',
        href: 'https://supabase.com/privacy',
      },
    ],
    children: [
      { _type: 'span', marks: ['strong'], text: 'Datenbank via Supabase:' },
      { _type: 'span', marks: [], text: ' Die von Ihnen im Tuning-Konfigurator eingegebenen Daten werden in einer Datenbank bei ' },
      { _type: 'span', marks: ['strong'], text: 'Supabase Inc.' },
      { _type: 'span', marks: [], text: ' (970 Toa Payoh North #07-04, Singapore 318992) gespeichert. Die Datenübermittlung ist durch Standardvertragsklauseln abgesichert. Details finden Sie in der ' },
      { _type: 'span', marks: ['supabase_link'], text: 'Datenschutzerklärung von Supabase' },
      { _type: 'span', marks: [], text: '.' },
    ],
  },
  {
    _type: 'block',
    style: 'normal',
    listItem: 'bullet',
    level: 1,
    _key: 'service_sanity',
    markDefs: [
      {
        _key: 'sanity_link',
        _type: 'link',
        href: 'https://www.sanity.io/legal/privacy',
      },
    ],
    children: [
      { _type: 'span', marks: ['strong'], text: 'Content Management via Sanity:' },
      { _type: 'span', marks: [], text: ' Die Inhalte unserer Webseite (Texte, Bilder) werden über ' },
      { _type: 'span', marks: ['strong'], text: 'Sanity Inc.' },
      { _type: 'span', marks: [], text: ' (2325 3rd Street, Suite 215, San Francisco, CA 94107, USA) verwaltet. Die Datenübermittlung in die USA ist durch Standardvertragsklauseln abgesichert. Details finden Sie in der ' },
      { _type: 'span', marks: ['sanity_link'], text: 'Datenschutzerklärung von Sanity' },
      { _type: 'span', marks: [], text: '.' },
    ],
  },
  {
    _type: 'block',
    style: 'h3',
    _key: 'cookies_header',
    markDefs: [],
    children: [{ _type: 'span', marks: ['strong'], text: 'Cookies' }],
  },
  {
    _type: 'block',
    style: 'normal',
    _key: 'cookies_text',
    markDefs: [],
    children: [
      {
        _type: 'span',
        marks: [],
        text: 'Diese Webseite verwendet Cookies. Cookies sind kleine Textdateien, die auf Ihrem Gerät gespeichert werden, um die Funktionalität der Seite zu gewährleisten und Ihre Erfahrung zu verbessern. Über den Cookie-Banner, der beim ersten Besuch erscheint, informieren wir Sie über die Verwendung von Cookies und holen Ihre Einwilligung ein. Technisch notwendige Cookies werden zur Bereitstellung der Webseite benötigt.',
      },
    ],
  },
  {
    _type: 'block',
    style: 'h3',
    _key: 'rights_header',
    markDefs: [],
    children: [{ _type: 'span', marks: ['strong'], text: 'Ihre Rechte' }],
  },
  {
    _type: 'block',
    style: 'normal',
    _key: 'rights_text',
    markDefs: [],
    children: [
      {
        _type: 'span',
        marks: [],
        text: 'Gemäss dem Schweizer Datenschutzgesetz haben Sie das Recht auf Auskunft, Berichtigung und Löschung Ihrer Daten. Wenn Sie Ihre Rechte wahrnehmen möchten, kontaktieren Sie uns bitte über die oben angegebene E-Mail-Adresse.',
      },
    ],
  },
];

async function updateLegalPage(slug: string, newContent: any[]) {
  console.log(`Updating page with slug: ${slug}...`);

  // Find the document ID for the given slug
  const documents = await client.fetch<{ _id: string }[]>(`*[_type == 'legalPage' && slug.current == $slug]`, { slug });

  if (!documents || documents.length === 0) {
    console.error(`❌ Error: No document found with slug '${slug}'.`);
    return;
  }

  const docId = documents[0]._id;

  // Patch the document with the new content
  try {
    await client
      .patch(docId)
      .set({ 
        content: newContent,
        lastUpdated: new Date().toISOString()
      })
      .commit();
    console.log(`✅ Successfully updated document: ${docId}`);
  } catch (error) {
    console.error(`❌ Error updating document ${docId}:`, error);
  }
}

// Enhanced Privacy Policy with nDSG compliance and cookie details
const enhancedPrivacyContent = [
  {
    _type: 'block',
    _key: 'privacy-intro',
    children: [
      {
        _type: 'span',
        _key: 'span-intro',
        text: 'Datenschutzerklärung',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h1'
  },
  {
    _type: 'block',
    _key: 'privacy-updated',
    children: [
      {
        _type: 'span',
        _key: 'span-updated',
        text: 'Letzte Aktualisierung: ' + new Date().toLocaleDateString('de-CH'),
        marks: ['em']
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'privacy-intro-text',
    children: [
      {
        _type: 'span',
        _key: 'span-intro-text',
        text: 'Der Schutz Ihrer persönlichen Daten ist uns ein besonderes Anliegen. Diese Datenschutzerklärung informiert Sie über die Art, den Umfang und den Zweck der Verarbeitung personenbezogener Daten auf unserer Website. Wir verarbeiten Ihre Daten ausschliesslich auf Grundlage der gesetzlichen Bestimmungen (nDSG - neues Datenschutzgesetz der Schweiz, DSGVO).'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'responsible-header',
    children: [
      {
        _type: 'span',
        _key: 'span-responsible',
        text: '1. Verantwortlicher',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'responsible-content',
    children: [
      {
        _type: 'span',
        _key: 'span-responsible-content',
        text: 'Verantwortlicher für die Datenverarbeitung auf dieser Website ist:\n\nDB-Performance Garage Bytyci\nStauseestrasse 1\n5316 Leuggern\nSchweiz\n\nTelefon: +41 76 250 07 61\nE-Mail: <EMAIL>'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'data-collection-header',
    children: [
      {
        _type: 'span',
        _key: 'span-data-collection',
        text: '2. Datenerfassung auf unserer Website',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'server-logs-header',
    children: [
      {
        _type: 'span',
        _key: 'span-server-logs',
        text: '2.1 Server-Log-Dateien',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h3'
  },
  {
    _type: 'block',
    _key: 'server-logs-content',
    children: [
      {
        _type: 'span',
        _key: 'span-server-logs-content',
        text: 'Der Provider der Seiten erhebt und speichert automatisch Informationen in Server-Log-Dateien, die Ihr Browser automatisch an uns übermittelt. Dies sind:\n\n• Browsertyp und Browserversion\n• Verwendetes Betriebssystem\n• Referrer URL\n• Hostname des zugreifenden Rechners\n• Uhrzeit der Serveranfrage\n• IP-Adresse\n\nEine Zusammenführung dieser Daten mit anderen Datenquellen wird nicht vorgenommen. Die Daten werden nach einer statistischen Auswertung gelöscht.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'cookies-header',
    children: [
      {
        _type: 'span',
        _key: 'span-cookies',
        text: '3. Cookies und Tracking-Technologien',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'cookies-intro',
    children: [
      {
        _type: 'span',
        _key: 'span-cookies-intro',
        text: 'Unsere Website verwendet Cookies. Cookies sind kleine Textdateien, die auf Ihrem Endgerät gespeichert werden und die Ihr Browser speichert. Sie richten keinen Schaden an und enthalten keine Viren.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'cookie-categories-header',
    children: [
      {
        _type: 'span',
        _key: 'span-cookie-categories',
        text: '3.1 Cookie-Kategorien',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h3'
  },
  {
    _type: 'block',
    _key: 'necessary-cookies',
    children: [
      {
        _type: 'span',
        _key: 'span-necessary',
        text: 'Notwendige Cookies: Diese Cookies sind für das Funktionieren der Website unerlässlich. Sie ermöglichen grundlegende Funktionen wie Seitennavigation und Zugriff auf sichere Bereiche der Website. Ohne diese Cookies kann die Website nicht ordnungsgemäss funktionieren.\n\n• cookie_consent: Speichert Ihre Cookie-Einstellungen (Dauer: 1 Jahr)\n• session_id: Technische Session-Verwaltung (Dauer: Session)'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'functional-cookies',
    children: [
      {
        _type: 'span',
        _key: 'span-functional',
        text: 'Funktionale Cookies: Diese Cookies ermöglichen erweiterte Funktionen und Personalisierung der Website.\n\n• user_preferences: Speichert Ihre Benutzereinstellungen (Dauer: 6 Monate)'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'analytics-cookies',
    children: [
      {
        _type: 'span',
        _key: 'span-analytics',
        text: 'Analyse-Cookies: Diese Cookies helfen uns zu verstehen, wie Besucher mit der Website interagieren, indem sie Informationen anonym sammeln und melden.\n\n• _ga, _ga_*: Google Analytics Cookies (Dauer: 2 Jahre, Anbieter: Google)'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'marketing-cookies',
    children: [
      {
        _type: 'span',
        _key: 'span-marketing',
        text: 'Marketing-Cookies: Diese Cookies werden verwendet, um Ihnen relevante Werbung zu zeigen.\n\n• _fbp: Facebook Pixel (Dauer: 3 Monate, Anbieter: Meta/Facebook)'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'cookie-management',
    children: [
      {
        _type: 'span',
        _key: 'span-cookie-management',
        text: '3.2 Cookie-Verwaltung',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h3'
  },
  {
    _type: 'block',
    _key: 'cookie-management-content',
    children: [
      {
        _type: 'span',
        _key: 'span-cookie-management-content',
        text: 'Sie können Ihre Cookie-Einstellungen jederzeit anpassen. Besuchen Sie unsere Cookie-Einstellungsseite um Ihre Präferenzen zu verwalten. Sie können auch die Cookie-Einstellungen in Ihrem Browser ändern.'
      }
    ],
    markDefs: [
      {
        _type: 'link',
        _key: 'cookie-settings-link',
        href: '/cookie-einstellungen'
      }
    ],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'contact-forms-header',
    children: [
      {
        _type: 'span',
        _key: 'span-contact-forms',
        text: '4. Kontaktformular und E-Mail-Kontakt',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'contact-forms-content',
    children: [
      {
        _type: 'span',
        _key: 'span-contact-forms-content',
        text: 'Wenn Sie uns per Kontaktformular, E-Mail oder WhatsApp Anfragen zukommen lassen, werden Ihre Angaben aus dem Anfrageformular inklusive der von Ihnen dort angegebenen Kontaktdaten zwecks Bearbeitung der Anfrage und für den Fall von Anschlussfragen bei uns gespeichert. Diese Daten geben wir nicht ohne Ihre Einwilligung weiter.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'rights-header',
    children: [
      {
        _type: 'span',
        _key: 'span-rights',
        text: '5. Ihre Rechte',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'rights-content',
    children: [
      {
        _type: 'span',
        _key: 'span-rights-content',
        text: 'Sie haben jederzeit das Recht:\n\n• Auskunft über Ihre bei uns gespeicherten personenbezogenen Daten und deren Verarbeitung zu erhalten\n• Berichtigung unrichtiger personenbezogener Daten zu verlangen\n• Löschung Ihrer bei uns gespeicherten personenbezogenen Daten zu verlangen\n• Einschränkung der Datenverarbeitung zu verlangen\n• Widerspruch gegen die Verarbeitung Ihrer Daten bei uns einzulegen\n• Datenübertragbarkeit zu verlangen\n\nSofern Sie uns eine Einwilligung erteilt haben, können Sie diese jederzeit mit Wirkung für die Zukunft widerrufen.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'data-security-header',
    children: [
      {
        _type: 'span',
        _key: 'span-data-security',
        text: '6. Datensicherheit',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'data-security-content',
    children: [
      {
        _type: 'span',
        _key: 'span-data-security-content',
        text: 'Wir verwenden innerhalb des Website-Besuchs das verbreitete SSL-Verfahren (Secure Socket Layer) in Verbindung mit der jeweils höchsten Verschlüsselungsstufe, die von Ihrem Browser unterstützt wird. In der Regel handelt es sich dabei um eine 256-Bit-Verschlüsselung. Falls Ihr Browser keine 256-Bit-Verschlüsselung unterstützt, greifen wir stattdessen auf 128-Bit-v3-Technologie zurück.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'changes-header',
    children: [
      {
        _type: 'span',
        _key: 'span-changes',
        text: '7. Änderungen der Datenschutzerklärung',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'changes-content',
    children: [
      {
        _type: 'span',
        _key: 'span-changes-content',
        text: 'Wir behalten uns vor, diese Datenschutzerklärung anzupassen, damit sie stets den aktuellen rechtlichen Anforderungen entspricht oder um Änderungen unserer Leistungen in der Datenschutzerklärung umzusetzen. Für Ihren erneuten Besuch gilt dann die neue Datenschutzerklärung.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'contact-privacy-header',
    children: [
      {
        _type: 'span',
        _key: 'span-contact-privacy',
        text: '8. Fragen zum Datenschutz',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'contact-privacy-content',
    children: [
      {
        _type: 'span',
        _key: 'span-contact-privacy-content',
        text: 'Wenn Sie Fragen zum Datenschutz haben, schreiben Sie uns bitte eine E-Mail oder wenden Sie sich direkt an die für den Datenschutz verantwortliche Person in unserer Organisation:\n\nDB-Performance Garage Bytyci\nE-Mail: <EMAIL>\nTelefon: +41 76 250 07 61'
      }
    ],
    markDefs: [],
    style: 'normal'
  }
];

async function runUpdate() {
  console.log('⚖️  Starting legal content update...');
  if (!process.env.SANITY_API_WRITE_TOKEN) {
    console.error('❌ SANITY_API_WRITE_TOKEN is not set. Please check your .env.local file.');
    return;
  }

  await updateLegalPage('impressum', impressumContent);
  await updateLegalPage('datenschutz', enhancedPrivacyContent);

  console.log('🎉 Legal content update completed!');
  console.log('📋 Enhanced privacy policy with:');
  console.log('   - nDSG (Swiss Data Protection Act) compliance');
  console.log('   - Detailed cookie categories and descriptions');
  console.log('   - User rights under Swiss law');
}

runUpdate();
