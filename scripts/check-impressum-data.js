import { createClient } from '@sanity/client'

const projectId = 'uzktk1gg'
const adminToken = 'skkr72eqIhavsdBJHo4bW4tgRcgD0PJQ0LYieeR5ScPe3yiOPE5jCDXFke9w09FdAVItL43v1Z14CsSoq'

async function checkImpressumData() {
  const datasets = ['development', 'production']
  
  for (const dataset of datasets) {
    console.log(`\n📋 Checking impressum data in ${dataset} dataset...`)
    
    const client = createClient({
      projectId,
      dataset,
      useCdn: false,
      token: adminToken,
      apiVersion: '2024-01-01'
    })

    try {
      // Get the impressum document
      const document = await client.fetch(`*[_type == 'legalPage' && slug.current == 'impressum'][0]{
        _id,
        title,
        content[0..3],
        lastUpdated
      }`)

      if (!document) {
        console.log(`❌ No impressum document found in ${dataset}`)
        continue
      }

      console.log(`✅ Found impressum document:`)
      console.log(`   ID: ${document._id}`)
      console.log(`   Title: ${document.title}`)
      console.log(`   Last Updated: ${document.lastUpdated}`)
      console.log(`   Content blocks: ${document.content?.length || 0}`)
      
      if (document.content && document.content.length > 0) {
        console.log(`   First block text: "${document.content[0]?.children?.[0]?.text || 'No text'}"`)
        if (document.content[1]) {
          console.log(`   Second block text: "${document.content[1]?.children?.[0]?.text || 'No text'}"`)
        }
      }
      
    } catch (error) {
      console.error(`❌ Error checking ${dataset}:`, error.message)
    }
  }
}

checkImpressumData()
