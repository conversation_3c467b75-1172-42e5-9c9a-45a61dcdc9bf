import { createClient } from '@sanity/client'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config({ path: '.env.local' })

const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
  token: process.env.SANITY_API_WRITE_TOKEN!,
  apiVersion: '2024-01-01',
  useCdn: false,
})

const cookiePageContent = [
  {
    _type: 'block',
    _key: 'cookie-intro',
    children: [
      {
        _type: 'span',
        _key: 'span-intro',
        text: 'Hier können Sie Ihre Cookie-Präferenzen verwalten. Sie können jederzeit Ihre Einstellungen ändern. Beachten Sie, dass das Deaktivieren bestimmter Cookies die Funktionalität der Website beeinträchtigen kann.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'cookie-categories-header',
    children: [
      {
        _type: 'span',
        _key: 'span-categories',
        text: '<PERSON>ie-Kategorien',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'necessary-cookies-detail',
    children: [
      {
        _type: 'span',
        _key: 'span-necessary-detail',
        text: 'Notwendige Cookies: Diese Cookies sind für das Funktionieren der Website unerlässlich. Sie ermöglichen grundlegende Funktionen wie Seitennavigation und Zugriff auf sichere Bereiche der Website. Ohne diese Cookies kann die Website nicht ordnungsgemäß funktionieren. Diese Cookies können nicht deaktiviert werden.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'functional-cookies-detail',
    children: [
      {
        _type: 'span',
        _key: 'span-functional-detail',
        text: 'Funktionale Cookies: Diese Cookies ermöglichen erweiterte Funktionen und Personalisierung der Website. Sie können von uns oder von Drittanbietern gesetzt werden, deren Dienste wir auf unseren Seiten verwenden. Wenn Sie diese Cookies nicht zulassen, funktionieren einige oder alle dieser Dienste möglicherweise nicht ordnungsgemäß.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'analytics-cookies-detail',
    children: [
      {
        _type: 'span',
        _key: 'span-analytics-detail',
        text: 'Analyse-Cookies: Diese Cookies helfen uns zu verstehen, wie Besucher mit der Website interagieren, indem sie Informationen anonym sammeln und melden. Diese Informationen helfen uns, die Website zu verbessern und zu verstehen, welche Bereiche am beliebtesten sind.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'marketing-cookies-detail',
    children: [
      {
        _type: 'span',
        _key: 'span-marketing-detail',
        text: 'Marketing-Cookies: Diese Cookies werden verwendet, um Ihnen relevante Werbung zu zeigen. Sie können auch verwendet werden, um die Anzahl der Anzeigen zu begrenzen und die Wirksamkeit von Werbekampagnen zu messen. Ohne diese Cookies erhalten Sie möglicherweise weniger relevante Werbung.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'cookie-management-info',
    children: [
      {
        _type: 'span',
        _key: 'span-management-info',
        text: 'Cookie-Verwaltung in Ihrem Browser',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h3'
  },
  {
    _type: 'block',
    _key: 'browser-management',
    children: [
      {
        _type: 'span',
        _key: 'span-browser-management',
        text: 'Sie können Cookies auch direkt in Ihrem Browser verwalten. Die meisten Browser ermöglichen es Ihnen, Cookies zu blockieren oder zu löschen. Beachten Sie jedoch, dass das Blockieren von Cookies die Funktionalität dieser und anderer Websites beeinträchtigen kann.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'contact-info',
    children: [
      {
        _type: 'span',
        _key: 'span-contact-info',
        text: 'Bei Fragen zu unseren Cookie-Richtlinien oder Datenschutzpraktiken können Sie uns jederzeit über unser '
      },
      {
        _type: 'span',
        _key: 'span-contact-link',
        text: 'Kontaktformular',
        marks: ['strong']
      },
      {
        _type: 'span',
        _key: 'span-contact-end',
        text: ' erreichen.'
      }
    ],
    markDefs: [
      {
        _type: 'link',
        _key: 'contact-link',
        href: '/contact'
      }
    ],
    style: 'normal'
  }
]

async function setupCookiePage() {
  console.log('🍪 Setting up Cookie Settings page in Sanity...')

  if (!process.env.SANITY_API_WRITE_TOKEN) {
    console.error('❌ SANITY_API_WRITE_TOKEN is not set. Please check your .env.local file.')
    return
  }

  try {
    // Check if cookie settings page already exists
    const existingPage = await client.fetch(`*[_type == 'legalPage' && slug.current == 'cookie-einstellungen'][0]`)

    if (existingPage) {
      console.log('📄 Cookie settings page already exists, updating content...')
      
      await client
        .patch(existingPage._id)
        .set({
          title: 'Cookie-Einstellungen',
          content: cookiePageContent,
          lastUpdated: new Date().toISOString()
        })
        .commit()
      
      console.log('✅ Cookie settings page updated successfully!')
    } else {
      console.log('📄 Creating new cookie settings page...')
      
      const newPage = {
        _type: 'legalPage',
        title: 'Cookie-Einstellungen',
        slug: {
          _type: 'slug',
          current: 'cookie-einstellungen'
        },
        content: cookiePageContent,
        lastUpdated: new Date().toISOString()
      }

      const result = await client.create(newPage)
      console.log('✅ Cookie settings page created successfully!', result._id)
    }

    // Also update the privacy policy to include the correct link
    console.log('🔗 Updating privacy policy link...')
    
    const privacyPolicy = await client.fetch(`*[_type == 'legalPage' && slug.current == 'datenschutz'][0]`)
    
    if (privacyPolicy) {
      // Update the cookie management section to have the correct link
      const updatedContent = privacyPolicy.content.map((block: any) => {
        if (block._key === 'cookie-management-content') {
          return {
            ...block,
            markDefs: [
              {
                _type: 'link',
                _key: 'cookie-settings-link',
                href: '/cookie-einstellungen'
              }
            ]
          }
        }
        return block
      })

      await client
        .patch(privacyPolicy._id)
        .set({
          content: updatedContent,
          lastUpdated: new Date().toISOString()
        })
        .commit()
      
      console.log('✅ Privacy policy link updated!')
    }

  } catch (error) {
    console.error('❌ Error setting up cookie page:', error)
  }
}

// Run the setup
setupCookiePage().then(() => {
  console.log('🎉 Cookie page setup completed!')
  process.exit(0)
}).catch(error => {
  console.error('💥 Setup failed:', error)
  process.exit(1)
})
