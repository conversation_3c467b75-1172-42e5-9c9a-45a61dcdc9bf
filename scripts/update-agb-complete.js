import { createClient } from '@sanity/client'

const projectId = 'uzktk1gg'
const adminToken = 'skkr72eqIhavsdBJHo4bW4tgRcgD0PJQ0LYieeR5ScPe3yiOPE5jCDXFke9w09FdAVItL43v1Z14CsSoq'

// Vollständige AGB für DB-Performance Garage Bytyci
const completeAGBContent = [
  {
    _type: 'block',
    _key: 'agb-title',
    children: [
      {
        _type: 'span',
        _key: 'span-title',
        text: 'Allgemeine Geschäftsbedingungen',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h1'
  },
  {
    _type: 'block',
    _key: 'agb-updated',
    children: [
      {
        _type: 'span',
        _key: 'span-updated',
        text: 'Stand: ' + new Date().toLocaleDateString('de-CH'),
        marks: ['em']
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'intro',
    children: [
      {
        _type: 'span',
        _key: 'span-intro',
        text: 'Die nachstehenden Allgemeinen Geschäftsbedingungen (AGB) regeln die Geschäftsbeziehung zwischen der DB-Performance Garage Bytyci, Stauseestrasse 1, 5316 Leuggern (nachfolgend "DB-Performance") und dem Kunden. Mit der Auftragserteilung erkennt der Kunde diese Bedingungen als verbindlich an.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'section1-header',
    children: [
      {
        _type: 'span',
        _key: 'span-section1',
        text: '1. Geltungsbereich',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'section1-content',
    children: [
      {
        _type: 'span',
        _key: 'span-section1-content',
        text: 'Diese AGB gelten für alle Leistungen der DB-Performance, insbesondere:\n\n• Werkstattdienstleistungen (Reparaturen, Service, Wartung)\n• Chiptuning und Fahrzeugoptimierung\n• Fahrzeugaufbereitung und Detailing\n• Verkauf von Ersatzteilen und Zubehör\n• Fahrzeughandel (Neu- und Gebrauchtwagen)\n\nAbweichende Bedingungen des Kunden werden nur wirksam, wenn sie von DB-Performance schriftlich bestätigt werden.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'section2-header',
    children: [
      {
        _type: 'span',
        _key: 'span-section2',
        text: '2. Auftragserteilung und Vertragsschluss',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'section2-content',
    children: [
      {
        _type: 'span',
        _key: 'span-section2-content',
        text: 'Der Kunde hat die gewünschten Leistungen so genau wie möglich zu beschreiben. Kostenvoranschläge sind unverbindlich, sofern nicht ausdrücklich als verbindlich bezeichnet.\n\nZeigen sich während der Arbeiten zusätzliche Mängel oder wird ein grösserer Arbeitsaufwand erforderlich, holt DB-Performance nach Möglichkeit die Zustimmung des Kunden ein.\n\nBei Chiptuning und Fahrzeugmodifikationen ist der Kunde verpflichtet, vollständige und korrekte Angaben über das Fahrzeug zu machen.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'section3-header',
    children: [
      {
        _type: 'span',
        _key: 'span-section3',
        text: '3. Preise und Zahlungsbedingungen',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'section3-content',
    children: [
      {
        _type: 'span',
        _key: 'span-section3-content',
        text: 'Alle Preise verstehen sich in Schweizer Franken inklusive der gesetzlichen Mehrwertsteuer.\n\nArbeitszeit wird nach Aufwand berechnet. Der aktuelle Stundenansatz beträgt CHF 120.00 (inkl. MwSt.).\n\nRechnungen sind bei Fahrzeugübergabe bzw. innerhalb von 10 Tagen nach Rechnungsdatum ohne Abzug zu begleichen. Bei Zahlungsverzug werden Verzugszinsen von 5% p.a. berechnet.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'section4-header',
    children: [
      {
        _type: 'span',
        _key: 'span-section4',
        text: '4. Fahrzeugübergabe und -abholung',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'section4-content',
    children: [
      {
        _type: 'span',
        _key: 'span-section4-content',
        text: 'Der Kunde ist verpflichtet, sein Fahrzeug persönlich zu übergeben und alle bekannten Mängel anzuzeigen.\n\nDas Fahrzeug ist spätestens 5 Arbeitstage nach Fertigstellungsanzeige abzuholen. Bei verspäteter Abholung wird eine Standgebühr von CHF 20.00 pro Tag berechnet.\n\nDB-Performance ist berechtigt, Probe- und Überführungsfahrten durchzuführen.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'section5-header',
    children: [
      {
        _type: 'span',
        _key: 'span-section5',
        text: '5. Chiptuning und Fahrzeugmodifikationen',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'section5-content',
    children: [
      {
        _type: 'span',
        _key: 'span-section5-content',
        text: 'Bei Chiptuning und anderen Fahrzeugmodifikationen gelten besondere Bestimmungen:\n\n• Der Kunde ist für die Eintragung in die Fahrzeugpapiere verantwortlich\n• Die Herstellergarantie kann durch Modifikationen beeinträchtigt werden\n• Der Kunde muss seine Versicherung über Änderungen informieren\n• Modifikationen können die Strassenverkehrszulassung beeinflussen\n• DB-Performance übernimmt keine Haftung für Folgeschäden durch Tuning-Massnahmen'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'section6-header',
    children: [
      {
        _type: 'span',
        _key: 'span-section6',
        text: '6. Gewährleistung und Garantie',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'section6-content',
    children: [
      {
        _type: 'span',
        _key: 'span-section6-content',
        text: 'Für Werkstattleistungen gewährt DB-Performance 12 Monate Gewährleistung auf Material und Arbeitsleistung.\n\nFür Chiptuning und Modifikationen beträgt die Gewährleistung 6 Monate.\n\nVerschleissteile sind von der Gewährleistung ausgeschlossen. Mängel sind unverzüglich, spätestens binnen 7 Tagen nach Entdeckung zu rügen.\n\nBei berechtigten Mängelrügen erfolgt nach Wahl von DB-Performance Nachbesserung oder Ersatzlieferung.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'section7-header',
    children: [
      {
        _type: 'span',
        _key: 'span-section7',
        text: '7. Haftung',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'section7-content',
    children: [
      {
        _type: 'span',
        _key: 'span-section7-content',
        text: 'DB-Performance haftet nur bei Vorsatz und grober Fahrlässigkeit. Die Haftung für leichte Fahrlässigkeit ist ausgeschlossen, soweit gesetzlich zulässig.\n\nDie Haftung für Folgeschäden, entgangenen Gewinn und mittelbare Schäden ist ausgeschlossen.\n\nFür den Verlust von Gegenständen im Fahrzeug wird keine Haftung übernommen.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'section8-header',
    children: [
      {
        _type: 'span',
        _key: 'span-section8',
        text: '8. Eigentumsvorbehalt und Pfandrecht',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'section8-content',
    children: [
      {
        _type: 'span',
        _key: 'span-section8-content',
        text: 'Gelieferte Waren bleiben bis zur vollständigen Bezahlung Eigentum von DB-Performance.\n\nDB-Performance hat ein Pfandrecht an den in ihren Besitz gelangten Fahrzeugen und Gegenständen bis zur vollständigen Begleichung aller Forderungen.\n\nAusgewechselte Teile gehen in das Eigentum von DB-Performance über.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'section9-header',
    children: [
      {
        _type: 'span',
        _key: 'span-section9',
        text: '9. Datenschutz',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'section9-content',
    children: [
      {
        _type: 'span',
        _key: 'span-section9-content',
        text: 'DB-Performance verarbeitet personenbezogene Daten gemäss der Datenschutzerklärung und den gesetzlichen Bestimmungen.\n\nFahrzeugdaten können zur Durchführung von Tuning-Massnahmen ausgelesen und gespeichert werden.\n\nDer Kunde wird über die Datenverarbeitung informiert und kann seine Rechte gemäss Datenschutzgesetz geltend machen.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'section10-header',
    children: [
      {
        _type: 'span',
        _key: 'span-section10',
        text: '10. Schlussbestimmungen',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'section10-content',
    children: [
      {
        _type: 'span',
        _key: 'span-section10-content',
        text: 'Es gilt schweizerisches Recht unter Ausschluss des UN-Kaufrechts.\n\nGerichtsstand ist Leuggern, Schweiz.\n\nSollten einzelne Bestimmungen unwirksam sein, bleibt die Gültigkeit der übrigen Bestimmungen unberührt.\n\nÄnderungen dieser AGB bedürfen der Schriftform.'
      }
    ],
    markDefs: [],
    style: 'normal'
  }
];

async function updateAGB() {
  const datasets = ['development', 'production']
  
  for (const dataset of datasets) {
    console.log(`📋 Updating AGB in ${dataset} dataset...`)
    
    const client = createClient({
      projectId,
      dataset,
      useCdn: false,
      token: adminToken,
      apiVersion: '2024-01-01'
    })

    try {
      // Find the AGB document
      const documents = await client.fetch(`*[_type == 'legalPage' && slug.current == 'agb']`)

      if (!documents || documents.length === 0) {
        console.log(`❌ No AGB document found in ${dataset}`)
        continue
      }

      const docId = documents[0]._id
      console.log(`📋 Current AGB ID: ${docId}`)

      // Update the document with complete content
      const result = await client
        .patch(docId)
        .set({ 
          content: completeAGBContent,
          lastUpdated: new Date().toISOString()
        })
        .commit()

      console.log(`✅ Successfully updated AGB in ${dataset} dataset`)
      
    } catch (error) {
      console.error(`❌ Error updating ${dataset}:`, error.message)
    }
  }
}

updateAGB().then(() => {
  console.log('🎉 AGB update completed!')
  console.log('📋 Added comprehensive content:')
  console.log('   - Geltungsbereich für alle Services')
  console.log('   - Spezielle Chiptuning-Bestimmungen')
  console.log('   - Haftungsausschlüsse für Tuning')
  console.log('   - Gewährleistungsregelungen')
  console.log('   - Pfandrecht und Eigentumsvorbehalt')
  console.log('   - Datenschutz-Bestimmungen')
  console.log('   - Schweizer Recht und Gerichtsstand')
})
