import { createClient } from '@sanity/client'

const projectId = 'uzktk1gg'
const adminToken = 'skkr72eqIhavsdBJHo4bW4tgRcgD0PJQ0LYieeR5ScPe3yiOPE5jCDXFke9w09FdAVItL43v1Z14CsSoq'

// Korrektes Impressum basierend auf Moneyhouse-Daten
const correctImpressumContent = [
  {
    _type: 'block',
    _key: 'impressum-title',
    children: [
      {
        _type: 'span',
        _key: 'span-title',
        text: 'Impressum',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h1'
  },
  {
    _type: 'block',
    _key: 'company-header',
    children: [
      {
        _type: 'span',
        _key: 'span-company-header',
        text: 'Angaben gemäss Art. 3 Abs. 3 UWG',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'company-name',
    children: [
      {
        _type: 'span',
        _key: 'span-company-name',
        text: 'DB-Performance Garage Bytyci',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'address',
    children: [
      {
        _type: 'span',
        _key: 'span-address',
        text: 'Stauseestrasse 1\n5316 Leuggern\nSchweiz'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'contact-info',
    children: [
      {
        _type: 'span',
        _key: 'span-contact',
        text: 'Telefon: +41 76 250 07 61\nE-Mail: <EMAIL>\nWebsite: https://db-performance.ch'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'legal-header',
    children: [
      {
        _type: 'span',
        _key: 'span-legal-header',
        text: 'Rechtliche Angaben',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'owner',
    children: [
      {
        _type: 'span',
        _key: 'span-owner',
        text: 'Inhaber: Dibran Bytyci'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'legal-form',
    children: [
      {
        _type: 'span',
        _key: 'span-legal-form',
        text: 'Rechtsform: Einzelfirma'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'register-info',
    children: [
      {
        _type: 'span',
        _key: 'span-register',
        text: 'Handelsregister-Nr.: CH-400.2.604.249-2\nHandelsregisteramt: Aargau\nUID/MWST-Nr.: CHE-149.623.444'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'business-purpose',
    children: [
      {
        _type: 'span',
        _key: 'span-purpose',
        text: 'Geschäftszweck: Führen einer Autowerkstatt sowie Handel mit Fahrzeugen'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'industry',
    children: [
      {
        _type: 'span',
        _key: 'span-industry',
        text: 'Branche: Handel und Reparatur von Automobilen und Motorrädern'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'liability-header',
    children: [
      {
        _type: 'span',
        _key: 'span-liability-header',
        text: 'Haftungsausschluss',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'liability-content',
    children: [
      {
        _type: 'span',
        _key: 'span-liability',
        text: 'Der Autor übernimmt keinerlei Gewähr hinsichtlich der inhaltlichen Richtigkeit, Genauigkeit, Aktualität, Zuverlässigkeit und Vollständigkeit der Informationen.\n\nHaftungsansprüche gegen den Autor wegen Schäden materieller oder immaterieller Art, welche aus dem Zugriff oder der Nutzung bzw. Nichtnutzung der veröffentlichten Informationen, durch Missbrauch der Verbindung oder durch technische Störungen entstanden sind, werden ausgeschlossen.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'links-header',
    children: [
      {
        _type: 'span',
        _key: 'span-links-header',
        text: 'Haftung für Links',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'links-content',
    children: [
      {
        _type: 'span',
        _key: 'span-links',
        text: 'Verweise und Links auf Webseiten Dritter liegen ausserhalb unseres Verantwortungsbereichs. Es wird jegliche Verantwortung für solche Webseiten abgelehnt. Der Zugriff und die Nutzung solcher Webseiten erfolgen auf eigene Gefahr des Nutzers oder der Nutzerin.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'copyright-header',
    children: [
      {
        _type: 'span',
        _key: 'span-copyright-header',
        text: 'Urheberrechte',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'copyright-content',
    children: [
      {
        _type: 'span',
        _key: 'span-copyright',
        text: 'Die Urheber- und alle anderen Rechte an Inhalten, Bildern, Fotos oder anderen Dateien auf der Website gehören ausschliesslich der Firma DB-Performance Garage Bytyci oder den speziell genannten Rechtsinhabern. Für die Reproduktion jeglicher Elemente ist die schriftliche Zustimmung der Urheberrechtsträger im Voraus einzuholen.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'data-protection-header',
    children: [
      {
        _type: 'span',
        _key: 'span-data-protection-header',
        text: 'Datenschutz',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'data-protection-content',
    children: [
      {
        _type: 'span',
        _key: 'span-data-protection',
        text: 'Gestützt auf Artikel 13 der schweizerischen Bundesverfassung und die datenschutzrechtlichen Bestimmungen des Bundes (Datenschutzgesetz, DSG) hat jede Person Anspruch auf Schutz ihrer Privatsphäre sowie auf Schutz vor Missbrauch ihrer persönlichen Daten. Wir halten diese Bestimmungen ein. Persönliche Daten werden streng vertraulich behandelt und weder an Dritte verkauft noch weiter gegeben.\n\nDetaillierte Informationen finden Sie in unserer '
      },
      {
        _type: 'span',
        _key: 'span-privacy-link',
        text: 'Datenschutzerklärung',
        marks: ['strong']
      },
      {
        _type: 'span',
        _key: 'span-data-protection-end',
        text: '.'
      }
    ],
    markDefs: [
      {
        _type: 'link',
        _key: 'privacy-link',
        href: '/datenschutz'
      }
    ],
    style: 'normal'
  }
];

async function updateImpressum() {
  const datasets = ['development', 'production']
  
  for (const dataset of datasets) {
    console.log(`📄 Updating impressum in ${dataset} dataset...`)
    
    const client = createClient({
      projectId,
      dataset,
      useCdn: false,
      token: adminToken,
      apiVersion: '2024-01-01'
    })

    try {
      // Find the impressum document
      const documents = await client.fetch(`*[_type == 'legalPage' && slug.current == 'impressum']`)

      if (!documents || documents.length === 0) {
        console.log(`❌ No impressum document found in ${dataset}`)
        continue
      }

      const docId = documents[0]._id
      console.log(`📋 Current impressum ID: ${docId}`)

      // Update the document with correct content
      const result = await client
        .patch(docId)
        .set({ 
          content: correctImpressumContent,
          lastUpdated: new Date().toISOString()
        })
        .commit()

      console.log(`✅ Successfully updated impressum in ${dataset} dataset`)
      
    } catch (error) {
      console.error(`❌ Error updating ${dataset}:`, error.message)
    }
  }
}

updateImpressum().then(() => {
  console.log('🎉 Impressum update completed!')
  console.log('📋 Updated with correct company data:')
  console.log('   - DB-Performance Garage Bytyci')
  console.log('   - Inhaber: Dibran Bytyci')
  console.log('   - Stauseestrasse 1, 5316 Leuggern')
  console.log('   - Handelsregister-Nr.: CH-400.2.604.249-2')
  console.log('   - UID: CHE-149.623.444')
  console.log('   - Rechtsform: Einzelfirma')
  console.log('   - Vollständige rechtliche Angaben nach Schweizer Recht')
})
