import { createClient, SanityClient } from 'next-sanity';
import { config } from 'dotenv';

// Load environment variables from .env.local
config({ path: '.env.local' });

const client: SanityClient = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,
  apiVersion: process.env.NEXT_PUBLIC_SANITY_API_VERSION || '2024-01-01',
  useCdn: false,
  token: process.env.SANITY_API_WRITE_TOKEN,
});

// Erweiterte Datenschutzerklärung nach nDSG (Schweizer Datenschutzgesetz)
const enhancedPrivacyContent = [
  {
    _type: 'block',
    _key: 'privacy-intro',
    children: [
      {
        _type: 'span',
        _key: 'span-intro',
        text: 'Datenschutzerklärung',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h1'
  },
  {
    _type: 'block',
    _key: 'privacy-updated',
    children: [
      {
        _type: 'span',
        _key: 'span-updated',
        text: 'Letzte Aktualisierung: ' + new Date().toLocaleDateString('de-CH'),
        marks: ['em']
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'privacy-intro-text',
    children: [
      {
        _type: 'span',
        _key: 'span-intro-text',
        text: 'Der Schutz Ihrer persönlichen Daten ist uns ein besonderes Anliegen. Diese Datenschutzerklärung informiert Sie über die Art, den Umfang und den Zweck der Verarbeitung personenbezogener Daten auf unserer Website. Wir verarbeiten Ihre Daten ausschliesslich auf Grundlage der gesetzlichen Bestimmungen (nDSG - neues Datenschutzgesetz der Schweiz, DSGVO).'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'responsible-header',
    children: [
      {
        _type: 'span',
        _key: 'span-responsible',
        text: '1. Verantwortlicher',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'responsible-content',
    children: [
      {
        _type: 'span',
        _key: 'span-responsible-content',
        text: 'Verantwortlicher für die Datenverarbeitung auf dieser Website ist:\n\nDB-Performance Garage Bytyci\n[Adresse]\n[PLZ Ort]\nSchweiz\n\nTelefon: +41 76 250 07 61\nE-Mail: <EMAIL>'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'data-collection-header',
    children: [
      {
        _type: 'span',
        _key: 'span-data-collection',
        text: '2. Datenerfassung auf unserer Website',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'server-logs-header',
    children: [
      {
        _type: 'span',
        _key: 'span-server-logs',
        text: '2.1 Server-Log-Dateien',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h3'
  },
  {
    _type: 'block',
    _key: 'server-logs-content',
    children: [
      {
        _type: 'span',
        _key: 'span-server-logs-content',
        text: 'Der Provider der Seiten erhebt und speichert automatisch Informationen in Server-Log-Dateien, die Ihr Browser automatisch an uns übermittelt. Dies sind:\n\n• Browsertyp und Browserversion\n• Verwendetes Betriebssystem\n• Referrer URL\n• Hostname des zugreifenden Rechners\n• Uhrzeit der Serveranfrage\n• IP-Adresse\n\nEine Zusammenführung dieser Daten mit anderen Datenquellen wird nicht vorgenommen. Die Daten werden nach einer statistischen Auswertung gelöscht.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'cookies-header',
    children: [
      {
        _type: 'span',
        _key: 'span-cookies',
        text: '3. Cookies und Tracking-Technologien',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'cookies-intro',
    children: [
      {
        _type: 'span',
        _key: 'span-cookies-intro',
        text: 'Unsere Website verwendet Cookies. Cookies sind kleine Textdateien, die auf Ihrem Endgerät gespeichert werden und die Ihr Browser speichert. Sie richten keinen Schaden an und enthalten keine Viren.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'cookie-categories-header',
    children: [
      {
        _type: 'span',
        _key: 'span-cookie-categories',
        text: '3.1 Cookie-Kategorien',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h3'
  },
  {
    _type: 'block',
    _key: 'necessary-cookies',
    children: [
      {
        _type: 'span',
        _key: 'span-necessary',
        text: 'Notwendige Cookies: Diese Cookies sind für das Funktionieren der Website unerlässlich. Sie ermöglichen grundlegende Funktionen wie Seitennavigation und Zugriff auf sichere Bereiche der Website. Ohne diese Cookies kann die Website nicht ordnungsgemäss funktionieren.\n\n• cookie_consent: Speichert Ihre Cookie-Einstellungen (Dauer: 1 Jahr)\n• session_id: Technische Session-Verwaltung (Dauer: Session)'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'functional-cookies',
    children: [
      {
        _type: 'span',
        _key: 'span-functional',
        text: 'Funktionale Cookies: Diese Cookies ermöglichen erweiterte Funktionen und Personalisierung der Website.\n\n• user_preferences: Speichert Ihre Benutzereinstellungen (Dauer: 6 Monate)'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'analytics-cookies',
    children: [
      {
        _type: 'span',
        _key: 'span-analytics',
        text: 'Analyse-Cookies: Diese Cookies helfen uns zu verstehen, wie Besucher mit der Website interagieren, indem sie Informationen anonym sammeln und melden.\n\n• _ga, _ga_*: Google Analytics Cookies (Dauer: 2 Jahre, Anbieter: Google)'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'marketing-cookies',
    children: [
      {
        _type: 'span',
        _key: 'span-marketing',
        text: 'Marketing-Cookies: Diese Cookies werden verwendet, um Ihnen relevante Werbung zu zeigen.\n\n• _fbp: Facebook Pixel (Dauer: 3 Monate, Anbieter: Meta/Facebook)'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'cookie-management',
    children: [
      {
        _type: 'span',
        _key: 'span-cookie-management',
        text: '3.2 Cookie-Verwaltung',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h3'
  },
  {
    _type: 'block',
    _key: 'cookie-management-content',
    children: [
      {
        _type: 'span',
        _key: 'span-cookie-management-content',
        text: 'Sie können Ihre Cookie-Einstellungen jederzeit anpassen. Besuchen Sie unsere '
      },
      {
        _type: 'span',
        _key: 'span-cookie-link',
        text: 'Cookie-Einstellungsseite',
        marks: ['strong']
      },
      {
        _type: 'span',
        _key: 'span-cookie-management-end',
        text: ' um Ihre Präferenzen zu verwalten. Sie können auch die Cookie-Einstellungen in Ihrem Browser ändern.'
      }
    ],
    markDefs: [
      {
        _type: 'link',
        _key: 'cookie-settings-link',
        href: '/cookie-einstellungen'
      }
    ],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'contact-forms-header',
    children: [
      {
        _type: 'span',
        _key: 'span-contact-forms',
        text: '4. Kontaktformular und E-Mail-Kontakt',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'contact-forms-content',
    children: [
      {
        _type: 'span',
        _key: 'span-contact-forms-content',
        text: 'Wenn Sie uns per Kontaktformular, E-Mail oder WhatsApp Anfragen zukommen lassen, werden Ihre Angaben aus dem Anfrageformular inklusive der von Ihnen dort angegebenen Kontaktdaten zwecks Bearbeitung der Anfrage und für den Fall von Anschlussfragen bei uns gespeichert. Diese Daten geben wir nicht ohne Ihre Einwilligung weiter.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'rights-header',
    children: [
      {
        _type: 'span',
        _key: 'span-rights',
        text: '5. Ihre Rechte',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'rights-content',
    children: [
      {
        _type: 'span',
        _key: 'span-rights-content',
        text: 'Sie haben jederzeit das Recht:\n\n• Auskunft über Ihre bei uns gespeicherten personenbezogenen Daten und deren Verarbeitung zu erhalten\n• Berichtigung unrichtiger personenbezogener Daten zu verlangen\n• Löschung Ihrer bei uns gespeicherten personenbezogenen Daten zu verlangen\n• Einschränkung der Datenverarbeitung zu verlangen\n• Widerspruch gegen die Verarbeitung Ihrer Daten bei uns einzulegen\n• Datenübertragbarkeit zu verlangen\n\nSofern Sie uns eine Einwilligung erteilt haben, können Sie diese jederzeit mit Wirkung für die Zukunft widerrufen.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'data-security-header',
    children: [
      {
        _type: 'span',
        _key: 'span-data-security',
        text: '6. Datensicherheit',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'data-security-content',
    children: [
      {
        _type: 'span',
        _key: 'span-data-security-content',
        text: 'Wir verwenden innerhalb des Website-Besuchs das verbreitete SSL-Verfahren (Secure Socket Layer) in Verbindung mit der jeweils höchsten Verschlüsselungsstufe, die von Ihrem Browser unterstützt wird. In der Regel handelt es sich dabei um eine 256-Bit-Verschlüsselung. Falls Ihr Browser keine 256-Bit-Verschlüsselung unterstützt, greifen wir stattdessen auf 128-Bit-v3-Technologie zurück.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'changes-header',
    children: [
      {
        _type: 'span',
        _key: 'span-changes',
        text: '7. Änderungen der Datenschutzerklärung',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'changes-content',
    children: [
      {
        _type: 'span',
        _key: 'span-changes-content',
        text: 'Wir behalten uns vor, diese Datenschutzerklärung anzupassen, damit sie stets den aktuellen rechtlichen Anforderungen entspricht oder um Änderungen unserer Leistungen in der Datenschutzerklärung umzusetzen. Für Ihren erneuten Besuch gilt dann die neue Datenschutzerklärung.'
      }
    ],
    markDefs: [],
    style: 'normal'
  },
  {
    _type: 'block',
    _key: 'contact-privacy-header',
    children: [
      {
        _type: 'span',
        _key: 'span-contact-privacy',
        text: '8. Fragen zum Datenschutz',
        marks: ['strong']
      }
    ],
    markDefs: [],
    style: 'h2'
  },
  {
    _type: 'block',
    _key: 'contact-privacy-content',
    children: [
      {
        _type: 'span',
        _key: 'span-contact-privacy-content',
        text: 'Wenn Sie Fragen zum Datenschutz haben, schreiben Sie uns bitte eine E-Mail oder wenden Sie sich direkt an die für den Datenschutz verantwortliche Person in unserer Organisation:\n\nDB-Performance Garage Bytyci\nE-Mail: <EMAIL>\nTelefon: +41 76 250 07 61'
      }
    ],
    markDefs: [],
    style: 'normal'
  }
];

async function updatePrivacyPolicy() {
  console.log('🔒 Updating privacy policy with nDSG compliance and cookie details...');

  if (!process.env.SANITY_API_WRITE_TOKEN) {
    console.error('❌ SANITY_API_WRITE_TOKEN is not set. Please check your .env.local file.');
    return;
  }

  try {
    // Find the privacy policy document
    const documents = await client.fetch<{ _id: string }[]>(`*[_type == 'legalPage' && slug.current == 'datenschutz']`);

    if (!documents || documents.length === 0) {
      console.error('❌ Error: No privacy policy document found with slug "datenschutz".');
      return;
    }

    const docId = documents[0]._id;

    // Update the document with enhanced content
    await client
      .patch(docId)
      .set({ 
        content: enhancedPrivacyContent,
        lastUpdated: new Date().toISOString()
      })
      .commit();

    console.log(`✅ Successfully updated privacy policy: ${docId}`);
    console.log('📋 Enhanced with:');
    console.log('   - nDSG (Swiss Data Protection Act) compliance');
    console.log('   - Detailed cookie categories and descriptions');
    console.log('   - User rights under Swiss law');
    console.log('   - Cookie management information');
    console.log('   - Data security measures');
    console.log('   - Contact information for privacy inquiries');

  } catch (error) {
    console.error('❌ Error updating privacy policy:', error);
  }
}

updatePrivacyPolicy();
