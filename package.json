{"name": "db-performance", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:safe": "SKIP_TYPECHECK=true SKIP_LINT=true next build", "build:strict": "pnpm lint && pnpm typecheck && next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "seed": "pnpm ts-node scripts/seed.ts", "import": "pnpm tsx scripts/import-vehicles.ts", "import:check": "pnpm tsx scripts/import-vehicles.ts --dry-run", "import-sma": "pnpm tsx scripts/import-sma-data.ts", "import-sma:dry": "pnpm tsx scripts/import-sma-data.ts --dry-run", "import-sma:clear": "pnpm tsx scripts/import-sma-data.ts --clear", "seed-services": "pnpm tsx scripts/seed-services.ts", "seed-homepage": "pnpm tsx scripts/seed-homepage.ts", "seed-tuning": "pnpm tsx scripts/seed-tuning-page.ts", "seed-site-settings": "pnpm tsx scripts/seed-site-settings.ts", "seed-all": "pnpm tsx scripts/seed-services.ts && pnpm tsx scripts/seed-homepage.ts && pnpm tsx scripts/seed-tuning-page.ts && pnpm tsx scripts/seed-site-settings.ts", "update-whatsapp": "pnpm tsx scripts/update-whatsapp-number.ts", "setup:cookies": "pnpm tsx scripts/setup-cookie-page.ts", "analyze-prices": "pnpm tsx scripts/analyze-prices.ts", "analyze": "ANALYZE=true next build", "typegen": "sanity schema extract --path=./src/sanity/extract.json && sanity typegen generate", "predev": "npm run typegen", "prebuild": "npm run typegen", "prepare": "husky install"}, "dependencies": {"@next/bundle-analyzer": "^15.3.5", "@portabletext/react": "^3.2.1", "@sanity/client": "^7.6.0", "@sanity/icons": "^3.7.4", "@sanity/image-url": "^1.1.0", "@sanity/ui": "^2.16.4", "@sanity/vision": "^3.98.1", "@supabase/supabase-js": "^2.50.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.0", "lucide-react": "^0.525.0", "next": "15.3.5", "next-sanity": "^9.12.0", "react": "^19.1.0", "react-dom": "^19.1.0", "sanity": "^3.98.1", "sanity-plugin-iframe-pane": "^3.2.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.0"}, "devDependencies": {"@playwright/test": "^1.53.2", "@sanity/codegen": "^3.98.1", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.0.12", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9.30.1", "eslint-config-next": "15.3.5", "eslint-plugin-testing-library": "^7.5.3", "husky": "^9.1.7", "jest": "^30.0.4", "jest-axe": "^10.0.0", "jest-environment-jsdom": "^30.0.4", "msw": "^2.10.3", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0"}